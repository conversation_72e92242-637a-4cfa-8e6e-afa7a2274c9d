import { prisma } from "../db";
import { successResponse, errorResponse } from "../utils/response.util";
import webSocketService from "./websocket.service";

interface AutoBidData {
  productId: string;
  maxBudget: number;
  bidIncrement: number;
}

class AutoBidService {
  /**
   * Enable auto-bid for a user on a product
   */
  async enableAutoBid(userId: string, data: AutoBidData) {
    try {
      const { productId, maxBudget, bidIncrement } = data;

      // Validate product exists and is an auction
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          bids: {
            orderBy: { amount: 'desc' },
            take: 1
          }
        }
         
      });

      if (!product) {
        return errorResponse("Product not found");
      }

      if (product.sellType !== "auction") {
        return errorResponse("Auto-bid is only available for auction items");
      }

      if (product.status !== "active") {
        return errorResponse("Product is not active");
      }

      // Check auction timing
      const now = new Date();
      if (product.auctionStartDate && now < new Date(product.auctionStartDate)) {
        return errorResponse("Auction has not started yet");
      }

      if (product.auctionEndDate && now > new Date(product.auctionEndDate)) {
        return errorResponse("Auction has ended");
      }

      // Validate max budget is higher than current bid
      const currentBid = product.currentBid ? Number(product.currentBid) : 0;
      if (maxBudget <= currentBid) {
        return errorResponse(`Max budget must be higher than current bid ($${currentBid})`);
      }

      // Validate bid increment
      if (bidIncrement <= 0) {
        return errorResponse("Bid increment must be greater than 0");
      }

      // Check if user already has auto-bid enabled for this product
      const existingAutoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        }
      });

      let autoBid;
      if (existingAutoBid) {
        // Update existing auto-bid
        autoBid = await prisma.autoBid.update({
          where: { id: existingAutoBid.id },
          data: {
            maxBudget,
            bidIncrement,
            isActive: true,
            updatedAt: new Date()
          }
        });
      } else {
        // Create new auto-bid
        autoBid = await prisma.autoBid.create({
          data: {
            productId,
            bidderId: userId,
            maxBudget,
            bidIncrement,
            isActive: true
          }
        });
      }

      // Place initial bid if current bid is 0 or user wants to bid immediately
      if (currentBid === 0 || maxBudget > currentBid + bidIncrement) {
        const initialBidAmount = Math.max(currentBid + bidIncrement, Number(product.priceUSD || 0));
        
        if (initialBidAmount <= maxBudget) {
          await this.placeBidForUser(userId, productId, initialBidAmount, 'auto');
        }
      }

      // Notify user about auto-bid activation
      webSocketService.notifyAutoBidActivated(userId, productId, {
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement)
      });

      return successResponse("Auto-bid enabled successfully", {
        autoBid: {
          ...autoBid,
          maxBudget: Number(autoBid.maxBudget),
          bidIncrement: Number(autoBid.bidIncrement)
        }
      });

    } catch (error) {
      console.error('Enable auto-bid error:', error);
      return errorResponse("Failed to enable auto-bid");
    }
  }

  /**
   * Disable auto-bid for a user on a product
   */
  async disableAutoBid(userId: string, productId: string) {
    try {
      const autoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        }
      });

      if (!autoBid) {
        return errorResponse("Auto-bid not found");
      }

      await prisma.autoBid.update({
        where: { id: autoBid.id },
        data: { isActive: false }
      });

      return successResponse("Auto-bid disabled successfully");

    } catch (error) {
      console.error('Disable auto-bid error:', error);
      return errorResponse("Failed to disable auto-bid");
    }
  }

  /**
   * Get user's auto-bid settings for a product
   */
  async getUserAutoBid(userId: string, productId: string) {
    try {
      const autoBid = await prisma.autoBid.findUnique({
        where: {
          productId_bidderId: {
            productId,
            bidderId: userId
          }
        },
        include: {
          product: {
            select: {
              id: true,
              itemName: true,
              currentBid: true,
              auctionEndDate: true
            }
          }
        }
      });

      if (!autoBid) {
        return successResponse("No auto-bid found", null);
      }

      return successResponse("Auto-bid retrieved successfully", {
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement)
      });

    } catch (error) {
      console.error('Get user auto-bid error:', error);
      return errorResponse("Failed to get auto-bid");
    }
  }

  /**
   * Process auto-bids when a new bid is placed
   */
  async processAutoBids(productId: string, newBidAmount: number, excludeBidderId?: string) {
    try {
      console.log(`Processing auto-bids for product ${productId}, new bid: ${newBidAmount}, exclude: ${excludeBidderId}`);

      // Get all active auto-bids for this product (excluding the bidder who just placed a bid)
      const autoBids = await prisma.autoBid.findMany({
        where: {
          productId,
          isActive: true,
          ...(excludeBidderId && { bidderId: { not: excludeBidderId } })
        },
        include: {
          bidder: {
            select: { id: true, firstName: true, lastName: true }
          }
        },
        orderBy: { maxBudget: 'desc' } // Process highest budget first
      });

      console.log(`Found ${autoBids.length} active auto-bids to process`);

      let currentHighestBid = newBidAmount;
      let processedBids = 0;

      // Process auto-bids in order of highest budget first
      for (const autoBid of autoBids) {
        const maxBudget = Number(autoBid.maxBudget);
        const bidIncrement = Number(autoBid.bidIncrement);

        console.log(`Processing auto-bid for user ${autoBid.bidderId}: maxBudget=${maxBudget}, increment=${bidIncrement}`);

        // Calculate next bid amount
        const nextBidAmount = currentHighestBid + bidIncrement;

        // Check if auto-bid budget allows this bid
        if (nextBidAmount <= maxBudget) {
          console.log(`Placing auto-bid: ${nextBidAmount} for user ${autoBid.bidderId}`);

          // Place auto-bid
          try {
            const bidResult = await this.placeBidForUser(autoBid.bidderId, productId, nextBidAmount, 'auto');

            if (bidResult) {
              // Update the current highest bid for next iteration
              currentHighestBid = nextBidAmount;
              processedBids++;

              console.log(`✅ Auto-bid placed successfully: ${nextBidAmount}`);

              // Notify user about auto-bid execution
              webSocketService.notifyAutoBidExecuted(autoBid.bidderId, productId, bidResult);

              // Add small delay to prevent race conditions
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          } catch (error) {
            console.log(`❌ Failed to place auto-bid: ${error}`);
          }
        } else {
          console.log(`Auto-bid budget exceeded: ${nextBidAmount} > ${maxBudget}`);
        }
      }

      console.log(`Auto-bid processing completed. Processed ${processedBids} bids.`);

      return successResponse("Auto-bids processed successfully", {
        processedBids,
        finalHighestBid: currentHighestBid
      });

    } catch (error) {
      console.error('Process auto-bids error:', error);
      return errorResponse("Failed to process auto-bids");
    }
  }

  /**
   * Place a bid for a user (used by auto-bid system)
   */
  private async placeBidForUser(bidderId: string, productId: string, bidAmount: number, bidType: 'manual' | 'auto' = 'auto') {
    try {
      // Create bid in transaction
      const result = await prisma.$transaction(async (tx) => {
        // Mark all previous bids as not winning
        await tx.bid.updateMany({
          where: { productId },
          data: { isWinning: false },
        });

        // Create new bid
        const bid = await tx.bid.create({
          data: {
            productId,
            bidderId,
            amount: bidAmount,
            bidType,
            isWinning: true,
          },
        });

        // Update product with new current bid and bid count
        await tx.product.update({
          where: { id: productId },
          data: {
            currentBid: bidAmount,
            bidCount: { increment: 1 },
          },
        });

        return bid;
      });

      return result;

    } catch (error) {
      console.error('Place bid for user error:', error);
      throw error;
    }
  }

  /**
   * Get all auto-bids for a user
   */
  async getUserAutoBids(userId: string) {
    try {
      const autoBids = await prisma.autoBid.findMany({
        where: { 
          bidderId: userId,
          isActive: true
        },
        include: {
          product: {
            select: {
              id: true,
              itemName: true,
              slug: true,
              currentBid: true,
              auctionEndDate: true,
              status: true,
              images: {
                where: { isMain: true },
                take: 1
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      });

      const transformedAutoBids = autoBids.map(autoBid => ({
        ...autoBid,
        maxBudget: Number(autoBid.maxBudget),
        bidIncrement: Number(autoBid.bidIncrement),
        product: {
          ...autoBid.product,
          currentBid: autoBid.product.currentBid ? Number(autoBid.product.currentBid) : null
        }
      }));

      return successResponse("User auto-bids retrieved successfully", transformedAutoBids);

    } catch (error) {
      console.error('Get user auto-bids error:', error);
      return errorResponse("Failed to get user auto-bids");
    }
  }
}

const autoBidService = new AutoBidService();
export default autoBidService;
