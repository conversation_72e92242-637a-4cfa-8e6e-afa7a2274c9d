import { errorResponse, successResponse } from '../utils/response.util';
import { prisma } from '../db';

interface ShippingCalculationData {
  fromCountry: string;
  fromCity: string;
  fromPostalCode: string;
  toCountry: string;
  toCity: string;
  toPostalCode: string;
  weight: number; // in kg
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  shippingMethod?: 'standard' | 'express' | 'overnight';
}

interface ShippingRate {
  carrier: string;
  service: string;
  cost: number;
  currency: string;
  estimatedDays: number;
  trackingAvailable: boolean;
}

class ShippingService {
  private readonly SHIPPO_API_KEY = process.env.SHIPPO_API_KEY;
  private readonly SHIPPO_BASE_URL = 'https://api.goshippo.com';

  /**
   * Calculate international shipping rates using Shippo API
   */
  async calculateShippingRates(data: ShippingCalculationData): Promise<any> {
    try {
      if (!this.SHIPPO_API_KEY) {
        // Fallback to manual calculation if no API key
        return this.calculateFallbackRates(data);
      }

      // Create address objects for Shippo
      const fromAddress = {
        name: "King Collectibles",
        street1: "123 Business St",
        city: data.fromCity,
        state: "",
        zip: data.fromPostalCode,
        country: data.fromCountry,
        phone: "****** 123 4567",
        email: "<EMAIL>"
      };

      const toAddress = {
        name: "Customer",
        street1: "123 Customer St",
        city: data.toCity,
        state: "",
        zip: data.toPostalCode,
        country: data.toCountry,
        phone: "****** 987 6543",
        email: "<EMAIL>"
      };

      // Create parcel object
      const parcel = {
        length: data.dimensions?.length || 10,
        width: data.dimensions?.width || 10,
        height: data.dimensions?.height || 5,
        distance_unit: "cm",
        weight: data.weight,
        mass_unit: "kg"
      };

      // Create shipment
      const shipmentData = {
        address_from: fromAddress,
        address_to: toAddress,
        parcels: [parcel],
        async: false
      };

      const response = await fetch(`${this.SHIPPO_BASE_URL}/shipments/`, {
        method: 'POST',
        headers: {
          'Authorization': `ShippoToken ${this.SHIPPO_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(shipmentData)
      });

      if (!response.ok) {
        console.error('Shippo API error:', await response.text());
        return this.calculateFallbackRates(data);
      }

      const shipment = await response.json();
      
      if (!shipment.rates || shipment.rates.length === 0) {
        return this.calculateFallbackRates(data);
      }

      // Transform Shippo rates to our format
      const rates: ShippingRate[] = shipment.rates.map((rate: any) => ({
        carrier: rate.provider,
        service: rate.servicelevel.name,
        cost: parseFloat(rate.amount),
        currency: rate.currency,
        estimatedDays: rate.estimated_days || 7,
        trackingAvailable: true
      }));

      return successResponse("Shipping rates calculated successfully", {
        rates: rates.slice(0, 5), // Return top 5 options
        fromAddress: data.fromCity + ', ' + data.fromCountry,
        toAddress: data.toCity + ', ' + data.toCountry,
        weight: data.weight
      });

    } catch (error) {
      console.error('Shipping calculation error:', error);
      return this.calculateFallbackRates(data);
    }
  }

  /**
   * Fallback shipping calculation when API is not available
   */
  private calculateFallbackRates(data: ShippingCalculationData): any {
    const baseRates = this.getBaseShippingRates(data.fromCountry, data.toCountry);
    const weightMultiplier = Math.max(1, Math.ceil(data.weight));
    
    const rates: ShippingRate[] = [
      {
        carrier: "International Standard",
        service: "Standard Shipping",
        cost: baseRates.standard * weightMultiplier,
        currency: "USD",
        estimatedDays: 14,
        trackingAvailable: true
      },
      {
        carrier: "International Express",
        service: "Express Shipping", 
        cost: baseRates.express * weightMultiplier,
        currency: "USD",
        estimatedDays: 7,
        trackingAvailable: true
      },
      {
        carrier: "International Priority",
        service: "Priority Shipping",
        cost: baseRates.priority * weightMultiplier,
        currency: "USD", 
        estimatedDays: 3,
        trackingAvailable: true
      }
    ];

    return successResponse("Shipping rates calculated (fallback)", {
      rates,
      fromAddress: data.fromCity + ', ' + data.fromCountry,
      toAddress: data.toCity + ', ' + data.toCountry,
      weight: data.weight,
      note: "Rates calculated using fallback method"
    });
  }

  /**
   * Get base shipping rates between countries
   */
  private getBaseShippingRates(fromCountry: string, toCountry: string) {
    // Base rates in USD
    const domesticRates = { standard: 5, express: 15, priority: 25 };
    const internationalRates = { standard: 25, express: 45, priority: 75 };
    const regionalRates = { standard: 15, express: 30, priority: 50 };

    // Same country
    if (fromCountry === toCountry) {
      return domesticRates;
    }

    // Regional shipping (same continent/region)
    const regions = {
      'US': ['CA', 'MX'],
      'GB': ['FR', 'DE', 'IT', 'ES', 'NL', 'BE'],
      'ID': ['MY', 'SG', 'TH', 'PH', 'VN'],
      'AU': ['NZ'],
      'JP': ['KR', 'CN', 'TW']
    };

    for (const [country, neighbors] of Object.entries(regions)) {
      if ((fromCountry === country && neighbors.includes(toCountry)) ||
          (toCountry === country && neighbors.includes(fromCountry))) {
        return regionalRates;
      }
    }

    return internationalRates;
  }

  /**
   * Get shipping options for checkout
   */
  async getShippingOptions(shippingAddressId: string, cartItems: any[]): Promise<any> {
    try {
      // Get shipping address
      const shippingAddress = await prisma.shippingAddress.findUnique({
        where: { id: shippingAddressId }
      });

      if (!shippingAddress) {
        return errorResponse("Shipping address not found");
      }

      // Calculate total weight from cart items
      const totalWeight = cartItems.reduce((total, item) => {
        // Assume each item weighs 0.5kg if not specified
        const itemWeight = item.product?.weight || 0.5;
        return total + (itemWeight * item.quantity);
      }, 0);

      // Calculate shipping from warehouse location
      const shippingData: ShippingCalculationData = {
        fromCountry: 'US', // Warehouse country
        fromCity: 'New York',
        fromPostalCode: '10001',
        toCountry: shippingAddress.country,
        toCity: shippingAddress.city,
        toPostalCode: shippingAddress.zipCode,
        weight: totalWeight
      };

      return await this.calculateShippingRates(shippingData);

    } catch (error) {
      console.error('Get shipping options error:', error);
      return errorResponse("Failed to get shipping options");
    }
  }

  /**
   * Track shipment
   */
  async trackShipment(trackingNumber: string): Promise<any> {
    try {
      if (!this.SHIPPO_API_KEY) {
        return errorResponse("Tracking service not available");
      }

      const response = await fetch(`${this.SHIPPO_BASE_URL}/tracks/${trackingNumber}`, {
        headers: {
          'Authorization': `ShippoToken ${this.SHIPPO_API_KEY}`,
        }
      });

      if (!response.ok) {
        return errorResponse("Failed to track shipment");
      }

      const tracking = await response.json();
      
      return successResponse("Tracking information retrieved", {
        trackingNumber,
        status: tracking.tracking_status?.status || 'unknown',
        location: tracking.tracking_status?.location || 'N/A',
        estimatedDelivery: tracking.eta,
        trackingHistory: tracking.tracking_history || []
      });

    } catch (error) {
      console.error('Track shipment error:', error);
      return errorResponse("Failed to track shipment");
    }
  }
}

const shippingService = new ShippingService();
export default shippingService;
