import { prisma } from "../db";
import { errorResponse, successResponse } from "../utils/response.util";

interface CurrencyRateData {
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  source: string;
  margin?: number;
}

class DailyCurrencyRateService {
  private readonly DEFAULT_MARGIN = 0.02; // 2% margin
  private readonly SUPPORTED_PAIRS = [
    { from: 'USD', to: 'IDR' },
    { from: 'IDR', to: 'USD' }
  ];

  /**
   * Fetch and save daily currency rates
   */
  async updateDailyRates() {
    try {
      console.log('Starting daily currency rate update...');
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Check if rates for today already exist
      const existingRates = await prisma.currencyRate.findMany({
        where: {
          date: today,
          isActive: true
        }
      });

      if (existingRates.length > 0) {
        console.log('Rates for today already exist, updating...');
      }

      // Fetch rates from multiple sources
      const rateResults = await Promise.allSettled([
        this.fetchFromExchangeRateAPI(),
        this.fetchFromFixerAPI(),
        this.fetchFromCurrencyAPI()
      ]);

      let bestRate = null;
      let bestSource = '';

      // Find the best rate (most reliable source)
      for (const result of rateResults) {
        if (result.status === 'fulfilled' && result.value) {
          bestRate = result.value.rate;
          bestSource = result.value.source;
          break;
        }
      }

      if (!bestRate) {
        console.error('Failed to fetch rates from all sources');
        return errorResponse("Failed to fetch currency rates");
      }

      // Calculate rates with margin
      const usdToIdrRate = bestRate;
      const idrToUsdRate = 1 / bestRate;

      // Save USD to IDR rate
      await this.saveCurrencyRate({
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        rate: usdToIdrRate,
        source: bestSource,
        margin: this.DEFAULT_MARGIN
      }, today);

      // Save IDR to USD rate
      await this.saveCurrencyRate({
        fromCurrency: 'IDR',
        toCurrency: 'USD',
        rate: idrToUsdRate,
        source: bestSource,
        margin: this.DEFAULT_MARGIN
      }, today);

      console.log(`Daily rates updated successfully. USD/IDR: ${usdToIdrRate}, Source: ${bestSource}`);

      return successResponse("Daily currency rates updated successfully", {
        usdToIdr: usdToIdrRate,
        idrToUsd: idrToUsdRate,
        source: bestSource,
        date: today.toISOString()
      });

    } catch (error) {
      console.error('Update daily rates error:', error);
      return errorResponse("Failed to update daily currency rates");
    }
  }

  /**
   * Save currency rate to database
   */
  private async saveCurrencyRate(data: CurrencyRateData, date: Date) {
    const { fromCurrency, toCurrency, rate, source, margin = this.DEFAULT_MARGIN } = data;

    // Calculate sell and buy rates with margin
    const sellRate = rate * (1 + margin); // Add margin for selling
    const buyRate = rate * (1 - margin);  // Subtract margin for buying

    // Upsert the rate
    await prisma.currencyRate.upsert({
      where: {
        fromCurrency_toCurrency_date: {
          fromCurrency,
          toCurrency,
          date
        }
      },
      update: {
        rate,
        sellRate,
        buyRate,
        margin,
        source,
        isActive: true,
        updatedAt: new Date()
      },
      create: {
        fromCurrency,
        toCurrency,
        rate,
        sellRate,
        buyRate,
        margin,
        source,
        date,
        isActive: true
      }
    });
  }

  /**
   * Get current active rates
   */
  async getCurrentRates() {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const rates = await prisma.currencyRate.findMany({
        where: {
          date: today,
          isActive: true
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      if (rates.length === 0) {
        // If no rates for today, get the latest rates
        const latestRates = await prisma.currencyRate.findMany({
          where: {
            isActive: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 2
        });

        return successResponse("Latest currency rates retrieved", {
          rates: latestRates,
          isToday: false
        });
      }

      return successResponse("Current currency rates retrieved", {
        rates,
        isToday: true
      });

    } catch (error) {
      console.error('Get current rates error:', error);
      return errorResponse("Failed to get current rates");
    }
  }

  /**
   * Convert currency using database rates
   */
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string, type: 'buy' | 'sell' = 'sell') {
    try {
      if (fromCurrency === toCurrency) {
        return successResponse("Currency conversion completed", {
          originalAmount: amount,
          convertedAmount: amount,
          rate: 1,
          type
        });
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const currencyRate = await prisma.currencyRate.findFirst({
        where: {
          fromCurrency,
          toCurrency,
          date: today,
          isActive: true
        }
      });

      if (!currencyRate) {
        // Fallback to latest rate
        const latestRate = await prisma.currencyRate.findFirst({
          where: {
            fromCurrency,
            toCurrency,
            isActive: true
          },
          orderBy: {
            date: 'desc'
          }
        });

        if (!latestRate) {
          return errorResponse("No currency rate found");
        }

        const rate = type === 'sell' ? Number(latestRate.sellRate) : Number(latestRate.buyRate);
        const convertedAmount = amount * rate;

        return successResponse("Currency conversion completed (using latest rate)", {
          originalAmount: amount,
          convertedAmount,
          rate,
          type,
          rateDate: latestRate.date
        });
      }

      const rate = type === 'sell' ? Number(currencyRate.sellRate) : Number(currencyRate.buyRate);
      const convertedAmount = amount * rate;

      return successResponse("Currency conversion completed", {
        originalAmount: amount,
        convertedAmount,
        rate,
        type,
        rateDate: currencyRate.date
      });

    } catch (error) {
      console.error('Convert currency error:', error);
      return errorResponse("Failed to convert currency");
    }
  }

  /**
   * Fetch rates from ExchangeRate-API
   */
  private async fetchFromExchangeRateAPI() {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      
      if (!response.ok) {
        throw new Error(`ExchangeRate-API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.rates && data.rates.IDR) {
        return {
          rate: data.rates.IDR,
          source: 'exchangerate-api.com'
        };
      }

      return null;
    } catch (error) {
      console.error('ExchangeRate-API failed:', error);
      return null;
    }
  }

  /**
   * Fetch rates from Fixer.io
   */
  private async fetchFromFixerAPI() {
    try {
      const apiKey = process.env.FIXER_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(`https://api.fixer.io/latest?access_key=${apiKey}&base=USD&symbols=IDR`);
      
      if (!response.ok) {
        throw new Error(`Fixer API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.rates && data.rates.IDR) {
        return {
          rate: data.rates.IDR,
          source: 'fixer.io'
        };
      }

      return null;
    } catch (error) {
      console.error('Fixer API failed:', error);
      return null;
    }
  }

  /**
   * Fetch rates from CurrencyAPI
   */
  private async fetchFromCurrencyAPI() {
    try {
      const apiKey = process.env.CURRENCY_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(`https://api.currencyapi.com/v3/latest?apikey=${apiKey}&base_currency=USD&currencies=IDR`);
      
      if (!response.ok) {
        throw new Error(`CurrencyAPI error: ${response.status}`);
      }

      const data = await response.json();

      if (data.data && data.data.IDR && data.data.IDR.value) {
        return {
          rate: data.data.IDR.value,
          source: 'currencyapi.com'
        };
      }

      return null;
    } catch (error) {
      console.error('CurrencyAPI failed:', error);
      return null;
    }
  }

  /**
   * Get rate history
   */
  async getRateHistory(fromCurrency: string, toCurrency: string, days: number = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const rates = await prisma.currencyRate.findMany({
        where: {
          fromCurrency,
          toCurrency,
          date: {
            gte: startDate,
            lte: endDate
          },
          isActive: true
        },
        orderBy: {
          date: 'asc'
        }
      });

      return successResponse("Rate history retrieved successfully", {
        rates,
        period: {
          from: startDate,
          to: endDate,
          days
        }
      });

    } catch (error) {
      console.error('Get rate history error:', error);
      return errorResponse("Failed to get rate history");
    }
  }
}

export default new DailyCurrencyRateService();
