import { prisma } from "../db";

// Exchange rates (should be fetched from real-time API in production)
const EXCHANGE_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
};

// Supported currencies
export type SupportedCurrency = 'USD' | 'IDR';

class CurrencyService {
  private readonly EXCHANGE_API_KEY = process.env.EXCHANGE_RATE_API_KEY;
  private readonly FIXER_API_KEY = process.env.FIXER_API_KEY;
  private readonly WISE_API_KEY = process.env.WISE_API_KEY;

  // Cache for exchange rates (5 minutes cache)
  private ratesCache: { data: any; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

  /**
   * Get current exchange rates from multiple sources
   */
  async getExchangeRates() {
    try {
      // Check cache first
      if (this.ratesCache && (Date.now() - this.ratesCache.timestamp) < this.CACHE_DURATION) {
        console.log('Using cached exchange rates');
        return {
          success: true,
          data: this.ratesCache.data
        };
      }

      // Try multiple APIs in order of preference
      let rates = await this.getFromExchangeRateAPI();

      if (!rates) {
        rates = await this.getFromFixerAPI();
      }

      if (!rates) {
        rates = await this.getFromWiseAPI();
      }

      if (!rates) {
        console.warn('All exchange rate APIs failed, using fallback rates');
        rates = {
          USD_TO_IDR: EXCHANGE_RATES.USD_TO_IDR,
          IDR_TO_USD: EXCHANGE_RATES.IDR_TO_USD,
          lastUpdated: new Date().toISOString(),
          source: 'fallback'
        };
      }

      // Cache the rates
      this.ratesCache = {
        data: rates,
        timestamp: Date.now()
      };

      return {
        success: true,
        data: rates
      };
    } catch (error) {
      console.error('Failed to get exchange rates:', error);
      return {
        success: false,
        message: 'Failed to get exchange rates',
        data: {
          ...EXCHANGE_RATES,
          lastUpdated: new Date().toISOString(),
          source: 'fallback'
        }
      };
    }
  }

  /**
   * Get rates from ExchangeRate-API (free tier available)
   */
  private async getFromExchangeRateAPI() {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');

      if (!response.ok) {
        throw new Error(`ExchangeRate-API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.rates && data.rates.IDR) {
        const usdToIdr = data.rates.IDR;
        const idrToUsd = 1 / usdToIdr;

        return {
          USD_TO_IDR: usdToIdr,
          IDR_TO_USD: idrToUsd,
          lastUpdated: new Date().toISOString(),
          source: 'exchangerate-api.com'
        };
      }

      return null;
    } catch (error) {
      console.error('ExchangeRate-API failed:', error);
      return null;
    }
  }

  /**
   * Get rates from Fixer.io API
   */
  private async getFromFixerAPI() {
    try {
      if (!this.FIXER_API_KEY) {
        return null;
      }

      const response = await fetch(`https://api.fixer.io/latest?access_key=${this.FIXER_API_KEY}&base=USD&symbols=IDR`);

      if (!response.ok) {
        throw new Error(`Fixer API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.rates && data.rates.IDR) {
        const usdToIdr = data.rates.IDR;
        const idrToUsd = 1 / usdToIdr;

        return {
          USD_TO_IDR: usdToIdr,
          IDR_TO_USD: idrToUsd,
          lastUpdated: new Date().toISOString(),
          source: 'fixer.io'
        };
      }

      return null;
    } catch (error) {
      console.error('Fixer API failed:', error);
      return null;
    }
  }

  /**
   * Get rates from Wise API (formerly TransferWise)
   */
  private async getFromWiseAPI() {
    try {
      if (!this.WISE_API_KEY) {
        return null;
      }

      // Wise API requires authentication and has different endpoint structure
      const response = await fetch('https://api.wise.com/v1/rates?source=USD&target=IDR', {
        headers: {
          'Authorization': `Bearer ${this.WISE_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Wise API error: ${response.status}`);
      }

      const data = await response.json();

      if (data && data.length > 0 && data[0].rate) {
        const usdToIdr = data[0].rate;
        const idrToUsd = 1 / usdToIdr;

        return {
          USD_TO_IDR: usdToIdr,
          IDR_TO_USD: idrToUsd,
          lastUpdated: new Date().toISOString(),
          source: 'wise.com'
        };
      }

      return null;
    } catch (error) {
      console.error('Wise API failed:', error);
      return null;
    }
  }

  /**
   * Convert amount from one currency to another using real-time rates
   */
  async convertCurrency(
    amount: number,
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<number> {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    try {
      const ratesResult = await this.getExchangeRates();
      const rates = ratesResult.data;

      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        return amount * rates.USD_TO_IDR;
      } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
        return amount * rates.IDR_TO_USD;
      }

      return amount;
    } catch (error) {
      console.error('Currency conversion error:', error);
      // Fallback to static rates
      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        return amount * EXCHANGE_RATES.USD_TO_IDR;
      } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
        return amount * EXCHANGE_RATES.IDR_TO_USD;
      }
      return amount;
    }
  }

  /**
   * Convert amount synchronously using cached rates
   */
  convertCurrencySync(
    amount: number,
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): number {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Use cached rates if available
    const rates = this.ratesCache?.data || EXCHANGE_RATES;

    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      return amount * (rates.USD_TO_IDR || EXCHANGE_RATES.USD_TO_IDR);
    } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      return amount * (rates.IDR_TO_USD || EXCHANGE_RATES.IDR_TO_USD);
    }

    return amount;
  }

  /**
   * Get historical exchange rates for a specific date
   */
  async getHistoricalRates(date: string) {
    try {
      // Format date as YYYY-MM-DD
      const formattedDate = new Date(date).toISOString().split('T')[0];

      // Try ExchangeRate-API historical endpoint
      const response = await fetch(`https://api.exchangerate-api.com/v4/history/USD/${formattedDate}`);

      if (!response.ok) {
        throw new Error(`Historical rates API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.rates && data.rates.IDR) {
        const usdToIdr = data.rates.IDR;
        const idrToUsd = 1 / usdToIdr;

        return {
          success: true,
          data: {
            USD_TO_IDR: usdToIdr,
            IDR_TO_USD: idrToUsd,
            date: formattedDate,
            source: 'exchangerate-api.com'
          }
        };
      }

      throw new Error('No historical data available');
    } catch (error) {
      console.error('Failed to get historical rates:', error);
      return {
        success: false,
        message: 'Failed to get historical rates',
        data: null
      };
    }
  }

  /**
   * Get exchange rates for multiple currencies
   */
  async getMultipleCurrencyRates(baseCurrency: string = 'USD', targetCurrencies: string[] = ['IDR']) {
    try {
      const response = await fetch(`https://api.exchangerate-api.com/v4/latest/${baseCurrency}`);

      if (!response.ok) {
        throw new Error(`Multiple currency API error: ${response.status}`);
      }

      const data = await response.json();

      if (data.rates) {
        const filteredRates: { [key: string]: number } = {};

        targetCurrencies.forEach(currency => {
          if (data.rates[currency]) {
            filteredRates[currency] = data.rates[currency];
          }
        });

        return {
          success: true,
          data: {
            base: baseCurrency,
            rates: filteredRates,
            lastUpdated: new Date().toISOString(),
            source: 'exchangerate-api.com'
          }
        };
      }

      throw new Error('No rates data available');
    } catch (error) {
      console.error('Failed to get multiple currency rates:', error);
      return {
        success: false,
        message: 'Failed to get multiple currency rates',
        data: null
      };
    }
  }

  /**
   * Get currency conversion with rate information
   */
  async getConversionWithRate(
    amount: number,
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ) {
    try {
      const ratesResult = await this.getExchangeRates();
      const rates = ratesResult.data;

      let rate = 1;
      let convertedAmount = amount;

      if (fromCurrency === 'USD' && toCurrency === 'IDR') {
        rate = rates.USD_TO_IDR;
        convertedAmount = amount * rate;
      } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
        rate = rates.IDR_TO_USD;
        convertedAmount = amount * rate;
      }

      return {
        success: true,
        data: {
          originalAmount: amount,
          convertedAmount,
          fromCurrency,
          toCurrency,
          exchangeRate: rate,
          lastUpdated: rates.lastUpdated,
          source: rates.source
        }
      };
    } catch (error) {
      console.error('Conversion with rate error:', error);
      return {
        success: false,
        message: 'Failed to get conversion with rate',
        data: null
      };
    }
  }

  /**
   * Get product prices in specified currency
   */
  async getProductPricesInCurrency(
    productIds: string[],
    currency: SupportedCurrency
  ) {
    try {
      const products = await prisma.product.findMany({
        where: {
          id: {
            in: productIds
          }
        },
        select: {
          id: true,
          priceUSD: true,
        }
      });

      const convertedPrices = products.map(product => {
        const basePrice = Number(product.priceUSD);
        const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
        
        return {
          id: product.id,
          originalPrice: basePrice,
          originalCurrency: 'USD' as const,
          convertedPrice: convertedPrice,
          currency: currency,
        };
      });

      return {
        success: true,
        data: convertedPrices
      };
    } catch (error) {
      console.error('Failed to get product prices:', error);
      return {
        success: false,
        message: 'Failed to get product prices'
      };
    }
  }

  /**
   * Get all products with prices in specified currency
   */
  async getAllProductsWithCurrency(
    currency: SupportedCurrency,
    page: number = 1,
    limit: number = 20,
    filters?: {
      category?: string;
      minPrice?: number;
      maxPrice?: number;
      search?: string;
    }
  ) {
    try {
      const offset = (page - 1) * limit;
      
      // Build where clause
      const where: any = {};
      
      if (filters?.category) {
        where.categoryId = filters.category;
      }
      
      if (filters?.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } }
        ];
      }

      // For price filtering, we need to convert the filter prices to USD first
      if (filters?.minPrice || filters?.maxPrice) {
        const priceFilter: any = {};
        
        if (filters.minPrice) {
          const minPriceUSD = this.convertCurrency(filters.minPrice, currency, 'USD');
          priceFilter.gte = minPriceUSD;
        }
        
        if (filters.maxPrice) {
          const maxPriceUSD = this.convertCurrency(filters.maxPrice, currency, 'USD');
          priceFilter.lte = maxPriceUSD;
        }
        
        where.priceUSD = priceFilter;
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          include: {
            category: true,
            images: true,
            seller: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              }
            },
            bids: {
              orderBy: {
                amount: 'desc'
              },
              take: 1,
              include: {
                bidder: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  }
                }
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          skip: offset,
          take: limit,
        }),
        prisma.product.count({ where })
      ]);

      // Convert prices to requested currency
      const productsWithConvertedPrices = products.map(product => {
        const basePrice = Number(product.priceUSD);
        const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
        
        // Also convert bid amounts if any
        const convertedBids = product.bids.map(bid => ({
          ...bid,
          amount: this.convertCurrency(Number(bid.amount), 'USD', currency),
          originalAmount: Number(bid.amount),
          currency: currency,
        }));

        return {
          ...product,
          priceUSD: basePrice, // Keep original USD price
          price: convertedPrice, // Converted price
          currency: currency,
          bids: convertedBids,
        };
      });

      return {
        success: true,
        data: {
          products: productsWithConvertedPrices,
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
          },
          currency,
          exchangeRate: currency === 'USD' ? 1 : EXCHANGE_RATES.USD_TO_IDR,
        }
      };
    } catch (error) {
      console.error('Failed to get products with currency:', error);
      return {
        success: false,
        message: 'Failed to get products'
      };
    }
  }

  /**
   * Get single product with price in specified currency
   */
  async getProductWithCurrency(productId: string, currency: SupportedCurrency) {
    try {
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          category: true,
          images: true,
          seller: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            }
          },
          bids: {
            orderBy: {
              amount: 'desc'
            },
            include: {
              bidder: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                }
              }
            }
          }
        }
      });

      if (!product) {
        return {
          success: false,
          message: 'Product not found'
        };
      }

      const basePrice = Number(product.priceUSD);
      const convertedPrice = this.convertCurrency(basePrice, 'USD', currency);
      
      // Convert bid amounts
      const convertedBids = product.bids.map(bid => ({
        ...bid,
        amount: this.convertCurrency(Number(bid.amount), 'USD', currency),
        originalAmount: Number(bid.amount),
        currency: currency,
      }));

      const productWithConvertedPrice = {
        ...product,
        priceUSD: basePrice,
        price: convertedPrice,
        currency: currency,
        bids: convertedBids,
      };

      return {
        success: true,
        data: productWithConvertedPrice
      };
    } catch (error) {
      console.error('Failed to get product with currency:', error);
      return {
        success: false,
        message: 'Failed to get product'
      };
    }
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: SupportedCurrency): string {
    const locale = currency === 'USD' ? 'en-US' : 'id-ID';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currency === 'IDR' ? 0 : 2,
      maximumFractionDigits: currency === 'IDR' ? 0 : 2,
    }).format(amount);
  }

  /**
   * Get supported currencies
   */
  getSupportedCurrencies() {
    return [
      {
        code: 'USD',
        name: 'US Dollar',
        symbol: '$',
        locale: 'en-US',
      },
      {
        code: 'IDR',
        name: 'Indonesian Rupiah',
        symbol: 'Rp',
        locale: 'id-ID',
      }
    ];
  }
}

const currencyService = new CurrencyService();
export default currencyService;
