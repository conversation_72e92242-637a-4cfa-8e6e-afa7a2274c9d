import { Context } from 'hono';
import { errorResponse } from '../utils/response.util';
import autoBidService from '../services/autoBid.service';

class AutoBidController {
  /**
   * Enable auto-bid for a product
   */
  async enableAutoBid(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const body = await c.req.json();
      const { productId, maxBudget, bidIncrement } = body;

      // Validate required fields
      if (!productId || !maxBudget || !bidIncrement) {
        return c.json(errorResponse("Product ID, max budget, and bid increment are required"), 400);
      }

      // Validate numeric values
      if (isNaN(maxBudget) || isNaN(bidIncrement)) {
        return c.json(errorResponse("Max budget and bid increment must be valid numbers"), 400);
      }

      if (maxBudget <= 0 || bidIncrement <= 0) {
        return c.json(errorResponse("Max budget and bid increment must be greater than 0"), 400);
      }

      const result = await autoBidService.enableAutoBid(user.id, {
        productId,
        maxBudget: Number(maxBudget),
        bidIncrement: Number(bidIncrement)
      });

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Auto-bid enabled successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Enable auto-bid error:', error);
      return c.json(errorResponse("Failed to enable auto-bid"), 500);
    }
  }

  /**
   * Disable auto-bid for a product
   */
  async disableAutoBid(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await autoBidService.disableAutoBid(user.id, productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Auto-bid disabled successfully"
      });

    } catch (error) {
      console.error('Disable auto-bid error:', error);
      return c.json(errorResponse("Failed to disable auto-bid"), 500);
    }
  }

  /**
   * Get user's auto-bid settings for a product
   */
  async getUserAutoBid(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const productId = c.req.param('productId');

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      const result = await autoBidService.getUserAutoBid(user.id, productId);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Auto-bid settings retrieved successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Get user auto-bid error:', error);
      return c.json(errorResponse("Failed to get auto-bid settings"), 500);
    }
  }

  /**
   * Get all auto-bids for the authenticated user
   */
  async getUserAutoBids(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const result = await autoBidService.getUserAutoBids(user.id);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "User auto-bids retrieved successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Get user auto-bids error:', error);
      return c.json(errorResponse("Failed to get user auto-bids"), 500);
    }
  }

  /**
   * Update auto-bid settings
   */
  async updateAutoBid(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const productId = c.req.param('productId');
      const body = await c.req.json();
      const { maxBudget, bidIncrement } = body;

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      // Validate numeric values
      if (maxBudget !== undefined && (isNaN(maxBudget) || maxBudget <= 0)) {
        return c.json(errorResponse("Max budget must be a valid number greater than 0"), 400);
      }

      if (bidIncrement !== undefined && (isNaN(bidIncrement) || bidIncrement <= 0)) {
        return c.json(errorResponse("Bid increment must be a valid number greater than 0"), 400);
      }

      // For update, we can reuse the enable method with new values
      const result = await autoBidService.enableAutoBid(user.id, {
        productId,
        maxBudget: Number(maxBudget),
        bidIncrement: Number(bidIncrement)
      });

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Auto-bid settings updated successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Update auto-bid error:', error);
      return c.json(errorResponse("Failed to update auto-bid settings"), 500);
    }
  }

  /**
   * Get auto-bid statistics for a user
   */
  async getAutoBidStats(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      // Get user's auto-bids with additional statistics
      const autoBidsResult = await autoBidService.getUserAutoBids(user.id);
      
      if (!autoBidsResult.status) {
        return c.json(autoBidsResult, 400);
      }

      const autoBids = autoBidsResult.data || [];
      
      // Calculate statistics
      const stats = {
        totalAutoBids: autoBids.length,
        totalMaxBudget: autoBids.reduce((sum: number, autoBid: any) => sum + autoBid.maxBudget, 0),
        activeAuctions: autoBids.filter((autoBid: any) => {
          const now = new Date();
          const endDate = autoBid.product.auctionEndDate ? new Date(autoBid.product.auctionEndDate) : null;
          return autoBid.product.status === 'active' && (!endDate || now < endDate);
        }).length,
        endedAuctions: autoBids.filter((autoBid: any) => {
          const now = new Date();
          const endDate = autoBid.product.auctionEndDate ? new Date(autoBid.product.auctionEndDate) : null;
          return endDate && now > endDate;
        }).length
      };

      return c.json({
        status: true,
        message: "Auto-bid statistics retrieved successfully",
        data: {
          stats,
          autoBids
        }
      });

    } catch (error) {
      console.error('Get auto-bid stats error:', error);
      return c.json(errorResponse("Failed to get auto-bid statistics"), 500);
    }
  }
}

const autoBidController = new AutoBidController();
export default autoBidController;
