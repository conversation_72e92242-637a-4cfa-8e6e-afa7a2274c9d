import { Context } from 'hono';
import { errorResponse } from '../utils/response.util';
import shippingService from '../services/shipping.service';

class ShippingController {
  /**
   * Calculate shipping rates
   */
  async calculateRates(c: Context) {
    try {
      const body = await c.req.json();
      const {
        fromCountry,
        fromCity,
        fromPostalCode,
        toCountry,
        toCity,
        toPostalCode,
        weight,
        dimensions,
        shippingMethod
      } = body;

      // Validate required fields
      if (!fromCountry || !fromCity || !toCountry || !toCity || !weight) {
        return c.json(errorResponse("Missing required shipping information"), 400);
      }

      const result = await shippingService.calculateShippingRates({
        fromCountry,
        fromCity,
        fromPostalCode: fromPostalCode || '',
        toCountry,
        toCity,
        toPostalCode: toPostalCode || '',
        weight: Number(weight),
        dimensions,
        shippingMethod
      });

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Shipping rates calculated successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Calculate shipping rates error:', error);
      return c.json(errorResponse("Failed to calculate shipping rates"), 500);
    }
  }

  /**
   * Get shipping options for checkout
   */
  async getShippingOptions(c: Context) {
    try {
      const user = c.get('user');
      if (!user) {
        return c.json(errorResponse("Unauthorized"), 401);
      }

      const { shippingAddressId, cartItems } = await c.req.json();

      if (!shippingAddressId || !cartItems || !Array.isArray(cartItems)) {
        return c.json(errorResponse("Shipping address ID and cart items are required"), 400);
      }

      const result = await shippingService.getShippingOptions(shippingAddressId, cartItems);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Shipping options retrieved successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Get shipping options error:', error);
      return c.json(errorResponse("Failed to get shipping options"), 500);
    }
  }

  /**
   * Track shipment
   */
  async trackShipment(c: Context) {
    try {
      const trackingNumber = c.req.param('trackingNumber');

      if (!trackingNumber) {
        return c.json(errorResponse("Tracking number is required"), 400);
      }

      const result = await shippingService.trackShipment(trackingNumber);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Tracking information retrieved successfully",
        data: result.data
      });

    } catch (error) {
      console.error('Track shipment error:', error);
      return c.json(errorResponse("Failed to track shipment"), 500);
    }
  }

  /**
   * Get shipping zones and rates
   */
  async getShippingZones(c: Context) {
    try {
      const zones = [
        {
          id: 'domestic',
          name: 'Domestic (US)',
          countries: ['US'],
          baseRate: 5,
          description: 'Shipping within United States'
        },
        {
          id: 'north_america',
          name: 'North America',
          countries: ['CA', 'MX'],
          baseRate: 15,
          description: 'Canada and Mexico'
        },
        {
          id: 'europe',
          name: 'Europe',
          countries: ['GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK'],
          baseRate: 25,
          description: 'European Union and surrounding countries'
        },
        {
          id: 'asia_pacific',
          name: 'Asia Pacific',
          countries: ['JP', 'KR', 'CN', 'TW', 'SG', 'MY', 'TH', 'PH', 'VN', 'ID', 'AU', 'NZ'],
          baseRate: 30,
          description: 'Asia Pacific region'
        },
        {
          id: 'rest_of_world',
          name: 'Rest of World',
          countries: ['*'],
          baseRate: 45,
          description: 'All other countries'
        }
      ];

      return c.json({
        status: true,
        message: "Shipping zones retrieved successfully",
        data: { zones }
      });

    } catch (error) {
      console.error('Get shipping zones error:', error);
      return c.json(errorResponse("Failed to get shipping zones"), 500);
    }
  }

  /**
   * Estimate shipping cost for product
   */
  async estimateShippingCost(c: Context) {
    try {
      const { productId, toCountry, toCity, toPostalCode, quantity = 1 } = c.req.query();

      if (!productId || !toCountry) {
        return c.json(errorResponse("Product ID and destination country are required"), 400);
      }

      // Get product details (you might need to implement this in product service)
      // For now, use default weight
      const estimatedWeight = 0.5 * Number(quantity); // 0.5kg per item

      const shippingData = {
        fromCountry: 'US',
        fromCity: 'New York',
        fromPostalCode: '10001',
        toCountry: toCountry as string,
        toCity: toCity as string || 'Unknown',
        toPostalCode: toPostalCode as string || '',
        weight: estimatedWeight
      };

      const result = await shippingService.calculateShippingRates(shippingData);

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        status: true,
        message: "Shipping cost estimated successfully",
        data: {
          ...result.data,
          productId,
          quantity: Number(quantity)
        }
      });

    } catch (error) {
      console.error('Estimate shipping cost error:', error);
      return c.json(errorResponse("Failed to estimate shipping cost"), 500);
    }
  }
}

const shippingController = new ShippingController();
export default shippingController;
