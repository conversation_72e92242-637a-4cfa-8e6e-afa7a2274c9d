import { Context } from 'hono';
import dailyCurrencyRateService from '../services/dailyCurrencyRate.service';
import simpleCurrencyRateService from '../services/simpleCurrencyRate.service';
import { errorResponse } from '../utils/response.util';

class DailyCurrencyRateController {
  /**
   * Update daily currency rates (for cron job)
   */
  async updateDailyRates(c: Context) {
    try {
      const result = await dailyCurrencyRateService.updateDailyRates();
      
      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Update daily rates controller error:', error);
      return c.json(errorResponse("An error occurred while updating daily rates"), 500);
    }
  }

  /**
   * Get current active rates
   */
  async getCurrentRates(c: Context) {
    try {
      // Try main service first
      const result = await dailyCurrencyRateService.getCurrentRates();

      if (result.status) {
        return c.json(result);
      }

      // Fallback to simple service if main service fails
      console.log('Main currency service failed, using fallback...');
      const fallbackResult = await simpleCurrencyRateService.getCurrentRates();

      return c.json(fallbackResult);
    } catch (error) {
      console.error('Get current rates controller error:', error);

      // Last resort - use fallback service
      try {
        const fallbackResult = await simpleCurrencyRateService.getCurrentRates();
        return c.json(fallbackResult);
      } catch (fallbackError) {
        console.error('Fallback service also failed:', fallbackError);
        return c.json(errorResponse("An error occurred while getting current rates"), 500);
      }
    }
  }

  /**
   * Convert currency using database rates with API fallback
   */
  async convertCurrency(c: Context) {
    try {
      const body = await c.req.json();
      const { amount, fromCurrency, toCurrency, type = 'sell' } = body;

      // Validate required fields
      if (!amount || !fromCurrency || !toCurrency) {
        return c.json(errorResponse("Missing required fields: amount, fromCurrency, toCurrency"), 400);
      }

      // Validate amount
      const numAmount = Number(amount);
      if (isNaN(numAmount) || numAmount <= 0) {
        return c.json(errorResponse("Amount must be a positive number"), 400);
      }

      if (!['buy', 'sell'].includes(type)) {
        return c.json(errorResponse("Type must be 'buy' or 'sell'"), 400);
      }

      // Normalize currency codes
      const fromCurr = fromCurrency.toUpperCase().trim();
      const toCurr = toCurrency.toUpperCase().trim();

      // Try main service first
      const result = await dailyCurrencyRateService.convertCurrency(
        numAmount,
        fromCurr,
        toCurr,
        type
      );

      if (result && 'status' in result && result.status) {
        return c.json(result);
      }

      // If main service fails, try fallback service
      console.log('Main currency service failed, using fallback...');
      const fallbackResult = await simpleCurrencyRateService.convertCurrency(
        numAmount,
        fromCurr,
        toCurr,
        type
      );

      return c.json(fallbackResult);

    } catch (error) {
      console.error('Convert currency controller error:', error);

      // Last resort - try fallback service
      try {
        const body = await c.req.json();
        const { amount, fromCurrency, toCurrency, type = 'sell' } = body;

        const fallbackResult = await simpleCurrencyRateService.convertCurrency(
          Number(amount),
          fromCurrency.toUpperCase(),
          toCurrency.toUpperCase(),
          type
        );

        return c.json(fallbackResult);
      } catch (fallbackError) {
        console.error('Fallback conversion also failed:', fallbackError);
        return c.json(errorResponse("An error occurred while converting currency"), 500);
      }
    }
  }

  /**
   * Get rate history
   */
  async getRateHistory(c: Context) {
    try {
      const fromCurrency = c.req.query('from')?.toUpperCase() || 'USD';
      const toCurrency = c.req.query('to')?.toUpperCase() || 'IDR';
      const days = parseInt(c.req.query('days') || '30');

      if (days < 1 || days > 365) {
        return c.json(errorResponse("Days must be between 1 and 365"), 400);
      }

      const result = await dailyCurrencyRateService.getRateHistory(
        fromCurrency,
        toCurrency,
        days
      );

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json(result);
    } catch (error) {
      console.error('Get rate history controller error:', error);
      return c.json(errorResponse("An error occurred while getting rate history"), 500);
    }
  }

  /**
   * Manual rate update (admin only)
   */
  async manualRateUpdate(c: Context) {
    try {
      // Add admin authentication check here
      const user = c.get('user');
      if (!user || user.role !== 'admin') {
        return c.json(errorResponse("Unauthorized - Admin access required"), 403);
      }

      const result = await dailyCurrencyRateService.updateDailyRates();

      if (!result.status) {
        return c.json(result, 400);
      }

      return c.json({
        ...result,
        message: "Manual rate update completed successfully"
      });
    } catch (error) {
      console.error('Manual rate update controller error:', error);
      return c.json(errorResponse("An error occurred during manual rate update"), 500);
    }
  }

  /**
   * Test currency rate update (for development)
   */
  async testRateUpdate(c: Context) {
    try {
      // Only allow in development environment
      if (process.env.NODE_ENV === 'production') {
        return c.json(errorResponse("Test endpoint not available in production"), 403);
      }

      console.log('🧪 Testing currency rate update...');
      const result = await dailyCurrencyRateService.updateDailyRates();

      return c.json({
        ...result,
        message: "Test rate update completed",
        environment: process.env.NODE_ENV || 'development'
      });
    } catch (error) {
      console.error('Test rate update controller error:', error);
      return c.json(errorResponse("An error occurred during test rate update"), 500);
    }
  }

  /**
   * Get rate statistics
   */
  async getRateStatistics(c: Context) {
    try {
      const fromCurrency = c.req.query('from')?.toUpperCase() || 'USD';
      const toCurrency = c.req.query('to')?.toUpperCase() || 'IDR';
      const days = parseInt(c.req.query('days') || '7');

      const historyResult = await dailyCurrencyRateService.getRateHistory(
        fromCurrency,
        toCurrency,
        days
      );

      if (!historyResult.status) {
        return c.json(historyResult, 400);
      }

      const rates = historyResult.data.rates;
      
      if (rates.length === 0) {
        return c.json(errorResponse("No rate data available for statistics"), 404);
      }

      // Calculate statisticsfetch
      const rateValues = rates.map((r: any) => Number(r.rate));
      const sellRateValues = rates.map((r: any) => Number(r.sellRate));
      const buyRateValues = rates.map((r: any) => Number(r.buyRate));

      const statistics = {
        period: {
          from: historyResult.data.period.from,
          to: historyResult.data.period.to,
          days: historyResult.data.period.days
        },
        rate: {
          current: rateValues[rateValues.length - 1],
          min: Math.min(...rateValues),
          max: Math.max(...rateValues),
          average: rateValues.reduce((a, b) => a + b, 0) / rateValues.length,
          change: rateValues.length > 1 ? rateValues[rateValues.length - 1] - rateValues[0] : 0,
          changePercent: rateValues.length > 1 ? 
            ((rateValues[rateValues.length - 1] - rateValues[0]) / rateValues[0]) * 100 : 0
        },
        sellRate: {
          current: sellRateValues[sellRateValues.length - 1],
          min: Math.min(...sellRateValues),
          max: Math.max(...sellRateValues),
          average: sellRateValues.reduce((a, b) => a + b, 0) / sellRateValues.length
        },
        buyRate: {
          current: buyRateValues[buyRateValues.length - 1],
          min: Math.min(...buyRateValues),
          max: Math.max(...buyRateValues),
          average: buyRateValues.reduce((a, b) => a + b, 0) / buyRateValues.length
        },
        dataPoints: rates.length
      };

      return c.json({
        status: true,
        message: "Rate statistics retrieved successfully",
        data: statistics
      });

    } catch (error) {
      console.error('Get rate statistics controller error:', error);
      return c.json(errorResponse("An error occurred while getting rate statistics"), 500);
    }
  }

  /**
   * Test API connectivity
   */
  async testApiConnectivity(c: Context) {
    try {
      const result = await simpleCurrencyRateService.testApiConnectivity();
      return c.json(result);
    } catch (error) {
      console.error('Test API connectivity error:', error);
      return c.json(errorResponse("An error occurred while testing API connectivity"), 500);
    }
  }

  /**
   * Get live rate for comparison
   */
  async getLiveRate(c: Context) {
    try {
      const result = await simpleCurrencyRateService.getLiveRate();
      return c.json(result);
    } catch (error) {
      console.error('Get live rate error:', error);
      return c.json(errorResponse("An error occurred while getting live rate"), 500);
    }
  }

  /**
   * Create manual currency rate (for immediate use)
   */
  async createManualRate(c: Context) {
    try {
      const body = await c.req.json();
      const { fromCurrency, toCurrency, rate, margin } = body;

      // Validate required fields
      if (!fromCurrency || !toCurrency || !rate) {
        return c.json(errorResponse("Missing required fields: fromCurrency, toCurrency, rate"), 400);
      }

      const numRate = Number(rate);
      if (isNaN(numRate) || numRate <= 0) {
        return c.json(errorResponse("Rate must be a positive number"), 400);
      }

      const numMargin = margin ? Number(margin) : 0.02; // Default 2%
      if (isNaN(numMargin) || numMargin < 0 || numMargin > 0.1) {
        return c.json(errorResponse("Margin must be between 0 and 0.1 (10%)"), 400);
      }

      // Create rate entry
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const sellRate = numRate * (1 + numMargin);
      const buyRate = numRate * (1 - numMargin);

      // This would need to be implemented in the service
      const result = await dailyCurrencyRateService.createManualRate({
        fromCurrency: fromCurrency.toUpperCase(),
        toCurrency: toCurrency.toUpperCase(),
        rate: numRate,
        sellRate,
        buyRate,
        margin: numMargin,
        source: 'manual',
        date: today
      });

      return c.json(result);

    } catch (error) {
      console.error('Create manual rate error:', error);
      return c.json(errorResponse("An error occurred while creating manual rate"), 500);
    }
  }

  /**
   * Quick setup - Create today's USD-IDR rate from live API
   */
  async quickSetupRate(c: Context) {
    try {
      console.log('🚀 Quick setup: Fetching and creating today\'s USD-IDR rate...');

      // Get live rate
      const liveRateResult = await simpleCurrencyRateService.getLiveRate();

      if (!liveRateResult.status || !liveRateResult.data) {
        return c.json(errorResponse("Failed to fetch live rate for setup"), 400);
      }

      const liveRate = liveRateResult.data.liveRate;
      const margin = 0.02; // 2% margin

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Create USD to IDR rate
      const usdToIdrResult = await dailyCurrencyRateService.createManualRate({
        fromCurrency: 'USD',
        toCurrency: 'IDR',
        rate: liveRate,
        sellRate: liveRate * (1 + margin),
        buyRate: liveRate * (1 - margin),
        margin,
        source: 'quick_setup',
        date: today
      });

      // Create IDR to USD rate
      const idrToUsdRate = 1 / liveRate;
      const idrToUsdResult = await dailyCurrencyRateService.createManualRate({
        fromCurrency: 'IDR',
        toCurrency: 'USD',
        rate: idrToUsdRate,
        sellRate: idrToUsdRate * (1 + margin),
        buyRate: idrToUsdRate * (1 - margin),
        margin,
        source: 'quick_setup',
        date: today
      });

      return c.json({
        status: true,
        message: "Quick setup completed successfully",
        data: {
          usdToIdr: usdToIdrResult,
          idrToUsd: idrToUsdResult,
          liveRate,
          margin,
          setupDate: today.toISOString()
        }
      });

    } catch (error) {
      console.error('Quick setup rate error:', error);
      return c.json(errorResponse("An error occurred during quick setup"), 500);
    }
  }
}

const dailyCurrencyRateController = new DailyCurrencyRateController();
export default dailyCurrencyRateController;
