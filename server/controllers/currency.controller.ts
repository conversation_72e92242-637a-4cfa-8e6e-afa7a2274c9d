import { Context } from "hono";
import { errorResponse } from "../utils/response.util";
import currencyService, { SupportedCurrency } from "../services/currency.service";

class CurrencyController {
  /**
   * Get current exchange rates
   */
  async getExchangeRates(c: Context) {
    try {
      const result = await currencyService.getExchangeRates();

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get exchange rates"), 500);
      }

      return c.json({
        status: true,
        message: "Exchange rates retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error("Get exchange rates error:", error);
      return c.json(errorResponse(error.message || "Failed to get exchange rates"), 500);
    }
  }

  /**
   * Get supported currencies
   */
  async getSupportedCurrencies(c: Context) {
    try {
      const currencies = currencyService.getSupportedCurrencies();

      return c.json({
        status: true,
        message: "Supported currencies retrieved successfully",
        data: currencies
      });
    } catch (error: any) {
      console.error("Get supported currencies error:", error);
      return c.json(errorResponse(error.message || "Failed to get supported currencies"), 500);
    }
  }

  /**
   * Convert currency amount
   */
  async convertCurrency(c: Context) {
    try {
      const { amount, from, to } = await c.req.json();

      if (!amount || !from || !to) {
        return c.json(errorResponse("Amount, from, and to currencies are required"), 400);
      }

      if (!['USD', 'IDR'].includes(from) || !['USD', 'IDR'].includes(to)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const convertedAmount = await currencyService.convertCurrency(
        Number(amount),
        from as SupportedCurrency,
        to as SupportedCurrency
      );

      return c.json({
        status: true,
        message: "Currency converted successfully",
        data: {
          originalAmount: Number(amount),
          fromCurrency: from,
          convertedAmount,
          toCurrency: to,
          exchangeRate: from === to ? 1 : (from === 'USD' ? 15000 : 1/15000),
        }
      });
    } catch (error: any) {
      console.error("Convert currency error:", error);
      return c.json(errorResponse(error.message || "Failed to convert currency"), 500);
    }
  }

  /**
   * Get products with currency conversion
   */
  async getProductsWithCurrency(c: Context) {
    try {
      const { currency = 'USD', page = '1', limit = '20' } = c.req.query();
      const { category, minPrice, maxPrice, search } = c.req.query();

      if (!['USD', 'IDR'].includes(currency)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const filters: any = {};
      if (category) filters.category = category;
      if (search) filters.search = search;
      if (minPrice) filters.minPrice = Number(minPrice);
      if (maxPrice) filters.maxPrice = Number(maxPrice);

      const result = await currencyService.getAllProductsWithCurrency(
        currency as SupportedCurrency,
        parseInt(page),
        parseInt(limit),
        filters
      );

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get products"), 500);
      }

      return c.json({
        status: true,
        message: "Products retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error("Get products with currency error:", error);
      return c.json(errorResponse(error.message || "Failed to get products"), 500);
    }
  }

  /**
   * Get single product with currency conversion
   */
  async getProductWithCurrency(c: Context) {
    try {
      const productId = c.req.param('productId');
      const { currency = 'USD' } = c.req.query();

      if (!productId) {
        return c.json(errorResponse("Product ID is required"), 400);
      }

      if (!['USD', 'IDR'].includes(currency)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const result = await currencyService.getProductWithCurrency(
        productId,
        currency as SupportedCurrency
      );

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get product"), 404);
      }

      return c.json({
        status: true,
        message: "Product retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error("Get product with currency error:", error);
      return c.json(errorResponse(error.message || "Failed to get product"), 500);
    }
  }

  /**
   * Get product prices in specific currency
   */
  async getProductPrices(c: Context) {
    try {
      const { productIds, currency = 'USD' } = await c.req.json();

      if (!productIds || !Array.isArray(productIds)) {
        return c.json(errorResponse("Product IDs array is required"), 400);
      }

      if (!['USD', 'IDR'].includes(currency)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const result = await currencyService.getProductPricesInCurrency(
        productIds,
        currency as SupportedCurrency
      );

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get product prices"), 500);
      }

      return c.json({
        status: true,
        message: "Product prices retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error("Get product prices error:", error);
      return c.json(errorResponse(error.message || "Failed to get product prices"), 500);
    }
  }

  /**
   * Format currency amount
   */
  async formatCurrency(c: Context) {
    try {
      const { amount, currency } = c.req.query();

      if (!amount || !currency) {
        return c.json(errorResponse("Amount and currency are required"), 400);
      }

      if (!['USD', 'IDR'].includes(currency)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const formatted = currencyService.formatCurrency(
        Number(amount),
        currency as SupportedCurrency
      );

      return c.json({
        status: true,
        message: "Currency formatted successfully",
        data: {
          amount: Number(amount),
          currency,
          formatted
        }
      });
    } catch (error: any) {
      console.error("Format currency error:", error);
      return c.json(errorResponse(error.message || "Failed to format currency"), 500);
    }
  }

  /**
   * Get historical exchange rates
   */
  async getHistoricalRates(c: Context) {
    try {
      const { date } = c.req.query();

      if (!date) {
        return c.json(errorResponse("Date is required (YYYY-MM-DD format)"), 400);
      }

      const result = await currencyService.getHistoricalRates(date);

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get historical rates"), 400);
      }

      return c.json({
        status: true,
        message: "Historical rates retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error('Get historical rates error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Get multiple currency rates
   */
  async getMultipleCurrencyRates(c: Context) {
    try {
      const { base = 'USD', currencies = 'IDR' } = c.req.query();
      const targetCurrencies = currencies.split(',').map((c: string) => c.trim().toUpperCase());

      const result = await currencyService.getMultipleCurrencyRates(base, targetCurrencies);

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get multiple currency rates"), 400);
      }

      return c.json({
        status: true,
        message: "Multiple currency rates retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error('Get multiple currency rates error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }

  /**
   * Get conversion with detailed rate information
   */
  async getConversionWithRate(c: Context) {
    try {
      const { amount, from, to } = c.req.query();

      if (!amount || !from || !to) {
        return c.json(errorResponse("Amount, from, and to currencies are required"), 400);
      }

      if (!['USD', 'IDR'].includes(from) || !['USD', 'IDR'].includes(to)) {
        return c.json(errorResponse("Unsupported currency"), 400);
      }

      const result = await currencyService.getConversionWithRate(
        Number(amount),
        from as SupportedCurrency,
        to as SupportedCurrency
      );

      if (!result.success) {
        return c.json(errorResponse(result.message || "Failed to get conversion"), 400);
      }

      return c.json({
        status: true,
        message: "Conversion with rate retrieved successfully",
        data: result.data
      });
    } catch (error: any) {
      console.error('Get conversion with rate error:', error);
      return c.json(errorResponse("Internal server error"), 500);
    }
  }
}

const currencyController = new CurrencyController();
export default currencyController;
