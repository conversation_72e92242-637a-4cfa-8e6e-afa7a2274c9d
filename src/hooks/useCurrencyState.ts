import { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useExchangeRateQuery, useRefreshExchangeRateMutation, currencyQueryKeys } from '@/services/useCurrencyQuery';
import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext';

interface CurrencyState {
  currentCurrency: SupportedCurrency;
  exchangeRate: number;
  isLoading: boolean;
  lastUpdated: string | null;
  source: string;
}

/**
 * Hook untuk manage currency state dan exchange rate
 * Otomatis fetch dan simpan exchange rate dari backend
 */
export const useCurrencyState = (initialCurrency: SupportedCurrency = 'IDR') => {
  const [currentCurrency, setCurrentCurrency] = useState<SupportedCurrency>(initialCurrency);
  const queryClient = useQueryClient();

  // Get exchange rate dari backend (auto-fetch jika belum ada)
  const { data: exchangeRateData, isLoading: isLoadingRate, refetch: refetchRate } = useExchangeRateQuery(
    'USD', 
    currentCurrency
  );

  // Mutation untuk refresh rate
  const refreshRateMutation = useRefreshExchangeRateMutation();

  // Currency state
  const [currencyState, setCurrencyState] = useState<CurrencyState>({
    currentCurrency: initialCurrency,
    exchangeRate: 1,
    isLoading: false,
    lastUpdated: null,
    source: 'unknown'
  });

  // Update state ketika exchange rate data berubah
  useEffect(() => {
    if (exchangeRateData) {
      setCurrencyState(prev => ({
        ...prev,
        currentCurrency,
        exchangeRate: exchangeRateData.rate,
        isLoading: isLoadingRate,
        lastUpdated: exchangeRateData.lastUpdated,
        source: exchangeRateData.source
      }));
    }
  }, [exchangeRateData, currentCurrency, isLoadingRate]);

  /**
   * Switch currency dan fetch exchange rate baru
   */
  const switchCurrency = useCallback(async (newCurrency: SupportedCurrency) => {
    if (newCurrency === currentCurrency) return;

    console.log(`🔄 Switching currency from ${currentCurrency} to ${newCurrency}`);
    
    // Update state immediately
    setCurrentCurrency(newCurrency);
    setCurrencyState(prev => ({
      ...prev,
      currentCurrency: newCurrency,
      isLoading: true
    }));

    try {
      // Invalidate current rate queries
      await queryClient.invalidateQueries({ 
        queryKey: currencyQueryKeys.rates('USD', currentCurrency) 
      });

      // Fetch new rate
      await refetchRate();

      // Invalidate products queries to trigger re-fetch with new currency
      await queryClient.invalidateQueries({ 
        queryKey: currencyQueryKeys.products(newCurrency) 
      });

      console.log(`✅ Currency switched to ${newCurrency}`);
    } catch (error) {
      console.error('Error switching currency:', error);
      // Revert currency on error
      setCurrentCurrency(currentCurrency);
      setCurrencyState(prev => ({
        ...prev,
        currentCurrency,
        isLoading: false
      }));
    }
  }, [currentCurrency, queryClient, refetchRate]);

  /**
   * Force refresh exchange rate
   */
  const refreshExchangeRate = useCallback(async () => {
    console.log(`🔄 Force refreshing exchange rate for ${currentCurrency}`);
    
    setCurrencyState(prev => ({ ...prev, isLoading: true }));

    try {
      await refreshRateMutation.mutateAsync({
        fromCurrency: 'USD',
        toCurrency: currentCurrency
      });

      console.log(`✅ Exchange rate refreshed for ${currentCurrency}`);
    } catch (error) {
      console.error('Error refreshing exchange rate:', error);
    } finally {
      setCurrencyState(prev => ({ ...prev, isLoading: false }));
    }
  }, [currentCurrency, refreshRateMutation]);

  /**
   * Get formatted exchange rate info
   */
  const getExchangeRateInfo = useCallback(() => {
    return {
      rate: currencyState.exchangeRate,
      formatted: `1 USD = ${currencyState.exchangeRate.toLocaleString()} ${currentCurrency}`,
      lastUpdated: currencyState.lastUpdated,
      source: currencyState.source,
      isStale: currencyState.source === 'fallback'
    };
  }, [currencyState, currentCurrency]);

  /**
   * Convert price using current exchange rate
   */
  const convertPrice = useCallback((amount: number, fromCurrency: SupportedCurrency = 'USD') => {
    if (fromCurrency === currentCurrency) {
      return amount;
    }

    if (fromCurrency === 'USD' && currentCurrency === 'IDR') {
      return amount * currencyState.exchangeRate;
    }

    if (fromCurrency === 'IDR' && currentCurrency === 'USD') {
      return amount / currencyState.exchangeRate;
    }

    return amount;
  }, [currentCurrency, currencyState.exchangeRate]);

  /**
   * Format price with currency symbol
   */
  const formatPrice = useCallback((amount: number, currency?: SupportedCurrency) => {
    const targetCurrency = currency || currentCurrency;
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: targetCurrency === 'IDR' ? 'IDR' : 'USD',
      minimumFractionDigits: targetCurrency === 'IDR' ? 0 : 2,
      maximumFractionDigits: targetCurrency === 'IDR' ? 0 : 2,
    });

    return formatter.format(amount);
  }, [currentCurrency]);

  /**
   * Convert and format price
   */
  const convertAndFormatPrice = useCallback((amount: number, fromCurrency: SupportedCurrency = 'USD') => {
    const convertedAmount = convertPrice(amount, fromCurrency);
    return formatPrice(convertedAmount);
  }, [convertPrice, formatPrice]);

  return {
    // State
    currentCurrency,
    exchangeRate: currencyState.exchangeRate,
    isLoading: currencyState.isLoading || isLoadingRate,
    lastUpdated: currencyState.lastUpdated,
    source: currencyState.source,

    // Actions
    switchCurrency,
    refreshExchangeRate,

    // Utilities
    getExchangeRateInfo,
    convertPrice,
    formatPrice,
    convertAndFormatPrice,

    // Status
    isStale: currencyState.source === 'fallback',
    isReady: !currencyState.isLoading && currencyState.exchangeRate > 0,
  };
};

/**
 * Hook untuk get products dengan currency conversion
 */
export const useProductsWithCurrency = (currency: SupportedCurrency, params?: any) => {
  const queryClient = useQueryClient();

  // Invalidate dan refetch products ketika currency berubah
  useEffect(() => {
    queryClient.invalidateQueries({ 
      queryKey: currencyQueryKeys.products(currency, params) 
    });
  }, [currency, params, queryClient]);

  return {
    currency,
    isReady: true
  };
};

/**
 * Hook untuk currency conversion dengan caching
 */
export const useCurrencyConverter = () => {
  const queryClient = useQueryClient();

  const convertWithCache = useCallback(async (amount: number, fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency) => {
    if (fromCurrency === toCurrency) {
      return amount;
    }

    // Try to get from cache first
    const cacheKey = currencyQueryKeys.conversion(fromCurrency, toCurrency, amount);
    const cachedResult = queryClient.getQueryData(cacheKey);
    
    if (cachedResult) {
      return (cachedResult as any).convertedAmount;
    }

    // If not in cache, this would trigger a new query
    // For now, return the amount (fallback)
    console.warn('Currency conversion not in cache, using fallback');
    return amount;
  }, [queryClient]);

  return {
    convertWithCache
  };
};
