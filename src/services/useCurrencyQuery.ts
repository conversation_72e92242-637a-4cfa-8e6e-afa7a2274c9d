import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";

// Types
export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  lastUpdated: string;
}

export interface CurrencyConversion {
  amount: number;
  from: 'USD' | 'IDR';
  to: 'USD' | 'IDR';
  convertedAmount: number;
  rate: number;
}

// Query keys
export const currencyQueryKeys = {
  all: ['currency'] as const,
  rates: () => [...currencyQueryKeys.all, 'rates'] as const,
  conversion: (from: string, to: string, amount: number) => 
    [...currencyQueryKeys.all, 'conversion', from, to, amount] as const,
};

// Fallback exchange rates (updated periodically)
const FALLBACK_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
};

// Get exchange rates from backend API
export const useExchangeRatesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: currencyQueryKeys.rates(),
    queryFn: async (): Promise<{ USD_TO_IDR: number; IDR_TO_USD: number; lastUpdated: string; source?: string }> => {
      try {
        const response = await apiClient.get('/currency/rates');
        return response.data;
      } catch (error) {
        console.warn('Failed to fetch exchange rates from backend, using fallback:', error);
        // Return fallback rates if API fails
        return {
          ...FALLBACK_RATES,
          lastUpdated: new Date().toISOString(),
          source: 'fallback'
        };
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for real-time rates
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2, // Retry twice for real-time data
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchInterval: 5 * 60 * 1000, // Auto-refetch every 5 minutes
  });
};

// Currency conversion hook
export const useCurrencyConversion = (
  amount: number,
  from: 'USD' | 'IDR',
  to: 'USD' | 'IDR'
) => {
  const { data: rates, isLoading, error } = useExchangeRatesQuery();

  return useQuery({
    queryKey: currencyQueryKeys.conversion(from, to, amount),
    queryFn: (): CurrencyConversion => {
      if (!rates) {
        throw new Error('Exchange rates not available');
      }

      if (from === to) {
        return {
          amount,
          from,
          to,
          convertedAmount: amount,
          rate: 1,
        };
      }

      let convertedAmount: number;
      let rate: number;

      if (from === 'USD' && to === 'IDR') {
        rate = rates.USD_TO_IDR;
        convertedAmount = amount * rate;
      } else if (from === 'IDR' && to === 'USD') {
        rate = rates.IDR_TO_USD;
        convertedAmount = amount * rate;
      } else {
        throw new Error(`Unsupported conversion: ${from} to ${to}`);
      }

      return {
        amount,
        from,
        to,
        convertedAmount,
        rate,
      };
    },
    enabled: !!rates && amount > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get historical exchange rates
export const useHistoricalRatesQuery = (date: string, enabled: boolean = true) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['currency', 'historical', date],
    queryFn: async (): Promise<{ USD_TO_IDR: number; IDR_TO_USD: number; date: string; source: string }> => {
      const response = await apiClient.get(`/currency/historical?date=${date}`);
      return response.data;
    },
    enabled: enabled && !!date,
    staleTime: 60 * 60 * 1000, // 1 hour for historical data
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
  });
};

// Get multiple currency rates
export const useMultipleCurrencyRatesQuery = (
  baseCurrency: string = 'USD',
  targetCurrencies: string[] = ['IDR'],
  enabled: boolean = true
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['currency', 'multiple', baseCurrency, targetCurrencies],
    queryFn: async (): Promise<{ base: string; rates: { [key: string]: number }; lastUpdated: string; source: string }> => {
      const currencies = targetCurrencies.join(',');
      const response = await apiClient.get(`/currency/multiple?base=${baseCurrency}&currencies=${currencies}`);
      return response.data;
    },
    enabled: enabled && targetCurrencies.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get conversion with detailed rate information
export const useConversionWithRateQuery = (
  amount: number,
  fromCurrency: SupportedCurrency,
  toCurrency: SupportedCurrency,
  enabled: boolean = true
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['currency', 'conversion-detail', amount, fromCurrency, toCurrency],
    queryFn: async (): Promise<{
      originalAmount: number;
      convertedAmount: number;
      fromCurrency: string;
      toCurrency: string;
      exchangeRate: number;
      lastUpdated: string;
      source: string;
    }> => {
      const response = await apiClient.get(`/currency/conversion-detail?amount=${amount}&from=${fromCurrency}&to=${toCurrency}`);
      return response.data;
    },
    enabled: enabled && amount > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Real-time exchange rate hook with auto-refresh
export const useRealTimeExchangeRates = (refreshInterval: number = 30000) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: ['currency', 'realtime'],
    queryFn: async (): Promise<{ USD_TO_IDR: number; IDR_TO_USD: number; lastUpdated: string; source: string }> => {
      const response = await apiClient.get('/currency/rates');
      return response.data;
    },
    staleTime: 0, // Always consider stale for real-time data
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: refreshInterval,
    refetchIntervalInBackground: true,
    refetchOnWindowFocus: true,
  });
};

// Utility function for quick conversion without React Query
export const convertCurrency = (
  amount: number,
  from: 'USD' | 'IDR',
  to: 'USD' | 'IDR',
  rates?: { USD_TO_IDR: number; IDR_TO_USD: number }
): number => {
  if (from === to) return amount;

  const exchangeRates = rates || FALLBACK_RATES;

  if (from === 'USD' && to === 'IDR') {
    return amount * exchangeRates.USD_TO_IDR;
  } else if (from === 'IDR' && to === 'USD') {
    return amount * exchangeRates.IDR_TO_USD;
  }

  return amount;
};

// Format currency with proper locale
export const formatCurrency = (amount: number, currency: 'USD' | 'IDR'): string => {
  const locale = currency === 'USD' ? 'en-US' : 'id-ID';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
    maximumFractionDigits: currency === 'IDR' ? 0 : 2,
  }).format(amount);
};

// Get currency symbol
export const getCurrencySymbol = (currency: 'USD' | 'IDR'): string => {
  return currency === 'USD' ? '$' : 'Rp';
};

// Currency display component data
export const getCurrencyDisplayData = (currency: 'USD' | 'IDR') => {
  return {
    code: currency,
    symbol: getCurrencySymbol(currency),
    name: currency === 'USD' ? 'US Dollar' : 'Indonesian Rupiah',
    locale: currency === 'USD' ? 'en-US' : 'id-ID',
  };
};

// Hook for currency selector options
export const useCurrencyOptions = () => {
  return [
    {
      value: 'USD',
      label: 'USD ($)',
      symbol: '$',
      name: 'US Dollar',
    },
    {
      value: 'IDR',
      label: 'IDR (Rp)',
      symbol: 'Rp',
      name: 'Indonesian Rupiah',
    },
  ];
};

// Get products with currency conversion from backend
export const useProductsWithCurrencyQuery = (
  currency: 'USD' | 'IDR',
  params?: {
    page?: number;
    limit?: number;
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    search?: string;
  }
) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...currencyQueryKeys.all, 'products', currency, params],
    queryFn: async () => {
      const response = await apiClient.get('/currency/products', { params: { currency, ...params } });
      return response.data;
    },
    enabled: !!currency,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single product with currency conversion from backend
export const useProductWithCurrencyQuery = (productId: string, currency: 'USD' | 'IDR') => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...currencyQueryKeys.all, 'product', productId, currency],
    queryFn: async () => {
      const response = await apiClient.get(`/currency/products/${productId}`, {
        params: { currency }
      });
      return response.data;
    },
    enabled: !!productId && !!currency,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get supported currencies from backend
export const useSupportedCurrenciesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: [...currencyQueryKeys.all, 'supported'],
    queryFn: async () => {
      const response = await apiClient.get('/currency/supported');
      return response.data;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Convert currency mutation
export const useConvertCurrencyMutation = () => {
  const apiClient = useAuthenticatedApi();

  return useMutation({
    mutationFn: async ({ amount, from, to }: { amount: number; from: 'USD' | 'IDR'; to: 'USD' | 'IDR' }) => {
      const response = await apiClient.post('/currency/convert', { amount, from, to });
      return response.data;
    },
  });
};

// Real-time price display hook
export const useRealTimePriceDisplay = (
  baseAmount: number,
  baseCurrency: 'USD' | 'IDR',
  displayCurrency: 'USD' | 'IDR'
) => {
  const { data: rates } = useExchangeRatesQuery();

  const convertedAmount = convertCurrency(baseAmount, baseCurrency, displayCurrency, rates);

  return {
    originalAmount: baseAmount,
    originalCurrency: baseCurrency,
    displayAmount: convertedAmount,
    displayCurrency,
    formattedPrice: formatCurrency(convertedAmount, displayCurrency),
    exchangeRate: rates ? (baseCurrency === 'USD' ? rates.USD_TO_IDR : rates.IDR_TO_USD) : null,
    isRealTime: !!rates,
  };
};
