import { SupportedCurrency } from '@/contexts/CurrencyLanguageContext'

// Cache for exchange rates
let exchangeRatesCache: { [key: string]: number } = {}
let lastFetchTime = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Fallback exchange rates
const FALLBACK_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
} as const

export interface CurrencyConversionOptions {
  fromCurrency: SupportedCurrency
  toCurrency: SupportedCurrency
  amount: number
}

export interface ExchangeRateResponse {
  rates: { [key: string]: number }
  base: string
  timestamp: number
}

/**
 * Fetch real-time exchange rates from multiple APIs with fallback
 */
async function fetchExchangeRates(): Promise<{ [key: string]: number }> {
  const now = Date.now()

  // Return cached rates if still valid
  if (now - lastFetchTime < CACHE_DURATION && Object.keys(exchangeRatesCache).length > 0) {
    return exchangeRatesCache
  }

  try {
    // Try Wise API first (if available)
    const wiseRates = await fetchWiseRates()
    if (wiseRates) {
      exchangeRatesCache = wiseRates
      lastFetchTime = now
      return wiseRates
    }

    // Fallback to ExchangeRate-API (free tier)
    const exchangeRateApiRates = await fetchExchangeRateApiRates()
    if (exchangeRateApiRates) {
      exchangeRatesCache = exchangeRateApiRates
      lastFetchTime = now
      return exchangeRateApiRates
    }

    // Fallback to Fixer.io
    const fixerRates = await fetchFixerRates()
    if (fixerRates) {
      exchangeRatesCache = fixerRates
      lastFetchTime = now
      return fixerRates
    }

    throw new Error('All currency APIs failed')
  } catch (error) {
    console.error('Failed to fetch exchange rates:', error)

    // Use fallback rates
    return {
      'USD': 1,
      'IDR': FALLBACK_RATES.USD_TO_IDR,
    }
  }
}

/**
 * Fetch rates from Wise API (requires API key)
 */
async function fetchWiseRates(): Promise<{ [key: string]: number } | null> {
  const apiKey = process.env.NEXT_PUBLIC_WISE_API_KEY
  if (!apiKey) return null

  try {
    const response = await fetch('https://api.wise.com/v1/rates', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) return null

    const data = await response.json()

    // Transform Wise response to our format
    const rates: { [key: string]: number } = { 'USD': 1 }

    data.forEach((rate: any) => {
      if (rate.source === 'USD') {
        rates[rate.target] = rate.rate
      }
    })

    return rates
  } catch (error) {
    console.error('Wise API error:', error)
    return null
  }
}

/**
 * Fetch rates from ExchangeRate-API (free tier)
 */
async function fetchExchangeRateApiRates(): Promise<{ [key: string]: number } | null> {
  try {
    const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD')

    if (!response.ok) return null

    const data: ExchangeRateResponse = await response.json()

    return {
      'USD': 1,
      ...data.rates
    }
  } catch (error) {
    console.error('ExchangeRate-API error:', error)
    return null
  }
}

/**
 * Fetch rates from Fixer.io
 */
async function fetchFixerRates(): Promise<{ [key: string]: number } | null> {
  const apiKey = process.env.NEXT_PUBLIC_FIXER_API_KEY
  if (!apiKey) return null

  try {
    const response = await fetch(`https://api.fixer.io/latest?access_key=${apiKey}&base=USD`)

    if (!response.ok) return null

    const data = await response.json()

    if (!data.success) return null

    return {
      'USD': 1,
      ...data.rates
    }
  } catch (error) {
    console.error('Fixer.io API error:', error)
    return null
  }
}

/**
 * Convert amount from one currency to another using real-time rates
 */
export async function convertCurrency({ fromCurrency, toCurrency, amount }: CurrencyConversionOptions): Promise<number> {
  if (fromCurrency === toCurrency) {
    return amount
  }

  try {
    const rates = await fetchExchangeRates()

    const fromRate = rates[fromCurrency] || 1
    const toRate = rates[toCurrency] || 1

    // Convert to USD first, then to target currency
    const usdAmount = amount / fromRate
    const convertedAmount = usdAmount * toRate

    return convertedAmount
  } catch (error) {
    console.error('Currency conversion error:', error)

    // Fallback to static rates
    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      return amount * FALLBACK_RATES.USD_TO_IDR
    }

    if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      return amount * FALLBACK_RATES.IDR_TO_USD
    }

    return amount
  }
}

/**
 * Get current exchange rate between two currencies
 */
export async function getExchangeRate(fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): Promise<number> {
  if (fromCurrency === toCurrency) {
    return 1
  }

  try {
    const rates = await fetchExchangeRates()

    const fromRate = rates[fromCurrency] || 1
    const toRate = rates[toCurrency] || 1

    return toRate / fromRate
  } catch (error) {
    console.error('Exchange rate fetch error:', error)

    // Fallback rates
    if (fromCurrency === 'USD' && toCurrency === 'IDR') {
      return FALLBACK_RATES.USD_TO_IDR
    }

    if (fromCurrency === 'IDR' && toCurrency === 'USD') {
      return FALLBACK_RATES.IDR_TO_USD
    }

    return 1
  }
}

/**
 * Synchronous currency conversion using cached rates
 */
export function convertCurrencySync({ fromCurrency, toCurrency, amount }: CurrencyConversionOptions): number {
  if (fromCurrency === toCurrency) {
    return amount
  }

  // Use cached rates if available
  if (Object.keys(exchangeRatesCache).length > 0) {
    const fromRate = exchangeRatesCache[fromCurrency] || 1
    const toRate = exchangeRatesCache[toCurrency] || 1

    const usdAmount = amount / fromRate
    return usdAmount * toRate
  }

  // Fallback to static rates
  if (fromCurrency === 'USD' && toCurrency === 'IDR') {
    return amount * FALLBACK_RATES.USD_TO_IDR
  }

  if (fromCurrency === 'IDR' && toCurrency === 'USD') {
    return amount * FALLBACK_RATES.IDR_TO_USD
  }

  return amount
}

/**
 * Format price with proper currency symbol and locale
 */
export function formatPrice(amount: number, currency: SupportedCurrency): string {
  const locale = currency === 'USD' ? 'en-US' : 'id-ID'
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
    maximumFractionDigits: currency === 'IDR' ? 0 : 2,
  }).format(amount)
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: SupportedCurrency): string {
  return currency === 'USD' ? '$' : 'Rp'
}

/**
 * Convert and format price for display
 */
export function convertAndFormatPrice(
  amount: number, 
  fromCurrency: SupportedCurrency, 
  toCurrency: SupportedCurrency
): string {
  const convertedAmount = convertCurrency({ fromCurrency, toCurrency, amount })
  return formatPrice(convertedAmount, toCurrency)
}

/**
 * Parse price string and extract numeric value
 */
export function parsePrice(priceString: string): number {
  // Remove all non-numeric characters except decimal point and minus sign
  const numericString = priceString.replace(/[^0-9.-]+/g, '')
  return parseFloat(numericString) || 0
}

/**
 * Get exchange rate between two currencies (synchronous fallback)
 */
export function getExchangeRateSync(fromCurrency: SupportedCurrency, toCurrency: SupportedCurrency): number {
  if (fromCurrency === toCurrency) {
    return 1
  }

  if (fromCurrency === 'USD' && toCurrency === 'IDR') {
    return FALLBACK_RATES.USD_TO_IDR
  }

  if (fromCurrency === 'IDR' && toCurrency === 'USD') {
    return FALLBACK_RATES.IDR_TO_USD
  }

  return 1
}

/**
 * Update exchange rates (for future real-time API integration)
 */
export async function updateExchangeRates(): Promise<void> {
  // TODO: Implement real-time exchange rate fetching
  // This would typically call an external API like:
  // - https://api.exchangerate-api.com/
  // - https://openexchangerates.org/
  // - https://fixer.io/
  console.log('Exchange rates updated (placeholder)')
}
