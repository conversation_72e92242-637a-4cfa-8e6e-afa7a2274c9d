'use client'
import React from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Image,
  Spinner,
  Grid,
  GridItem,
  Checkbox,
} from '@chakra-ui/react'
import { usePaymentMethodsQuery } from '@/services/usePaymentQuery'
import { FaShieldAlt, FaLock, FaCreditCard, FaWallet, FaUniversity, FaStore } from 'react-icons/fa'
import { useTranslations } from 'next-intl'


interface PaymentMethodSelectorProps {
  currency: 'USD' | 'IDR'
  onMethodSelect: (method: string, type: 'invoice' | 'ewallet' | 'virtual_account' | 'retail_outlet' | 'credit_card', channel?: string) => void
  selectedMethod?: string
  selectedMethods?: string[]
  onMethodsChange?: (methods: string[]) => void
  amount?: number
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  currency,
  onMethodSelect,
  selectedMethod,
  selectedMethods = [],
  onMethodsChange,
  amount = 0
}) => {
  const t = useTranslations()
  const { data: paymentMethods, isLoading, error } = usePaymentMethodsQuery(currency)

  console.log("paymentMethods", paymentMethods)

  // Handle checkbox change
  const handleMethodToggle = (methodValue: string, isChecked: boolean) => {
    const [method, type, channel] = methodValue.split('|')

    if (isChecked) {
      // For payment methods, we typically only allow one selection
      // So we replace the current selection
      onMethodSelect(method, type as any, channel)
      console.log("Selected Method:", method, type, channel)
      if (onMethodsChange) {
        onMethodsChange([methodValue])
      }
    } else {
      if (onMethodsChange) {
        onMethodsChange(selectedMethods.filter(m => m !== methodValue))
      }
    }
  }

  const isMethodSelected = (methodValue: string): boolean => {
    return selectedMethods.includes(methodValue) || selectedMethod === methodValue.split('|')[0]
  }

  const getMethodIcon = (method: string) => {
    const icons: { [key: string]: string } = {
      'OVO': '/icons/ovo.png',
      'DANA': '/icons/dana.png',
      'LINKAJA': '/icons/linkaja.png',
      'SHOPEEPAY': '/icons/shopeepay.png',
      'BCA': '/icons/bca.png',
      'BNI': '/icons/bni.png',
      'BRI': '/icons/bri.png',
      'MANDIRI': '/icons/mandiri.png',
      'PERMATA': '/icons/permata.png',
      'ALFAMART': '/icons/alfamart.png',
      'INDOMARET': '/icons/indomaret.png',
    }
    return icons[method] || '/icons/default-payment.png'
  }

  if (isLoading) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">{t('Payment.method')}</Heading>
        </Card.Header>
        <Card.Body>
          <HStack>
            <Spinner size="sm" />
            <Text>Loading payment methods...</Text>
          </HStack>
        </Card.Body>
      </Card.Root>
    )
  }

  if (error || !paymentMethods) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">{t('Payment.method')}</Heading>
        </Card.Header>
        <Card.Body>
          <Text color="red.500">Failed to load payment methods</Text>
        </Card.Body>
      </Card.Root>
    )
  }

  return (
    <Card.Root>
      <Card.Header>
        <HStack justify="space-between" align="center">
          <VStack align="start" gap={1}>
            <Heading size="md">{t('Payment.chooseMethod')}</Heading>
            <Text fontSize="sm" color="gray.600">
              {t('Payment.availableFor')} {currency}
            </Text>
          </VStack>
          <HStack gap={2}>
            <FaShieldAlt color="green" />
            <Text fontSize="xs" color="green.600" fontWeight="medium">
              {t('Payment.securedBy')}
            </Text>
          </HStack>
        </HStack>
      </Card.Header>
      <Card.Body>
        <VStack align="stretch" gap={4}>
          {/* Xendit Invoice (Recommended) */}
          {paymentMethods.invoice && (
            <Box>
              <HStack mb={3}>
                <FaCreditCard />
                <Heading size="sm">Xendit Invoice</Heading>
                <Badge colorScheme="blue">Recommended</Badge>
              </HStack>
              <Box
                border="2px solid"
                borderColor={isMethodSelected('xendit_invoice|invoice') ? 'blue.500' : 'gray.200'}
                borderRadius="lg"
                p={4}
                bg={isMethodSelected('xendit_invoice|invoice') ? 'blue.50' : 'white'}
                cursor="pointer"
                transition="all 0.2s"
                _hover={{ borderColor: 'blue.300' }}
                onClick={() => handleMethodToggle('xendit_invoice|invoice', !isMethodSelected('xendit_invoice|invoice'))}
              >
                <HStack gap={3} align="center" w="full">
                  <Checkbox.Root
                    checked={isMethodSelected('xendit_invoice|invoice')}
                    onCheckedChange={({ checked }) => handleMethodToggle('xendit_invoice|invoice', !!checked)}
                    colorPalette="blue"
                  >
                    <Checkbox.HiddenInput />
                    <Checkbox.Control />
                  </Checkbox.Root>
                  <Box boxSize="32px">
                    <Image
                      src="/icons/xendit.png"
                      alt="Xendit"
                      boxSize="32px"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.setAttribute('style', 'display: block');
                      }}
                    />
                    <FaCreditCard size={32} style={{ display: 'none' }} />
                  </Box>
                  <VStack align="start" flex="1" gap={1}>
                    <Text fontWeight="bold">{t('Payment.allInOne')}</Text>
                    <Text fontSize="sm" color="gray.600">
                      {t('Payment.allInOneDesc')}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            </Box>
          )}

          {/* Credit Card */}
          {/* {paymentMethods.creditCard && (
            <Box>
              <HStack mb={3}>
                <FaCreditCard />
                <Heading size="sm">{t('Payment.creditCard')}</Heading>
              </HStack>
              <Box
                border="2px solid"
                borderColor={isMethodSelected('credit_card|credit_card') ? 'blue.500' : 'gray.200'}
                borderRadius="lg"
                p={4}
                bg={isMethodSelected('credit_card|credit_card') ? 'blue.50' : 'white'}
                cursor="pointer"
                transition="all 0.2s"
                _hover={{ borderColor: 'blue.300' }}
                onClick={() => handleMethodToggle('credit_card|credit_card', !isMethodSelected('credit_card|credit_card'))}
              >
                <HStack gap={3} align="center" w="full">
                  <Checkbox.Root
                    checked={isMethodSelected('credit_card|credit_card')}
                    onCheckedChange={({ checked }) => handleMethodToggle('credit_card|credit_card', !!checked)}
                    colorPalette="blue"
                  >
                    <Checkbox.HiddenInput />
                    <Checkbox.Control />
                  </Checkbox.Root>
                  <Box boxSize="32px">
                    <Image
                      src="/icons/credit-card.png"
                      alt="Credit Card"
                      boxSize="32px"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.setAttribute('style', 'display: block');
                      }}
                    />
                    <FaCreditCard size={32} style={{ display: 'none' }} />
                  </Box>
                  <VStack align="start" flex="1" gap={1}>
                    <Text fontWeight="bold">{t('Payment.creditCard')}</Text>
                    <Text fontSize="sm" color="gray.600">
                      {t('Payment.creditCardDesc')}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            </Box>
          )} */}

          {/* E-Wallets */}
          {paymentMethods.ewallet?.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaWallet />
                <Heading size="sm">{t('Payment.eWallets')}</Heading>
              </HStack>
              <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={3}>
                {paymentMethods.ewallet.map((wallet, idx) => (
                  <GridItem key={wallet.id}>
                    <Box
                      border="2px solid"
                      borderColor={isMethodSelected(`${wallet.name}|ewallet`) ? 'green.500' : 'gray.200'}
                      borderRadius="lg"
                      p={3}
                      bg={isMethodSelected(`${wallet.name}|ewallet`) ? 'green.50' : 'white'}
                      cursor="pointer"
                      transition="all 0.2s"
                      _hover={{ borderColor: 'green.300' }}
                      onClick={() => handleMethodToggle(`${wallet.name}|ewallet|${wallet.name}`, !isMethodSelected(`${wallet.name}|ewallet|${wallet.name}`))}
                    >
                      <VStack gap={2}>
                        <Checkbox.Root
                          checked={isMethodSelected(`${wallet.name}|ewallet|${wallet.name}`)}
                          onCheckedChange={({ checked }) => handleMethodToggle(`${wallet.name}|ewallet|${wallet.name}`, !!checked)}
                          colorPalette="green"
                        >
                          <Checkbox.HiddenInput />
                          <Checkbox.Control />
                        </Checkbox.Root>
                        <Box boxSize="40px">
                          <Image
                            src={getMethodIcon(wallet.name)}
                            alt={wallet.name}
                            boxSize="40px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.setAttribute('style', 'display: block');
                            }}
                          />
                          <FaWallet size={40} style={{ display: 'none' }} />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" textAlign="center">{wallet.name}</Text>
                      </VStack>
                    </Box>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

          {/* Virtual Account */}
          {paymentMethods.virtual_account?.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaUniversity />
                <Heading size="sm">{t('Payment.bankTransfer')}</Heading>
              </HStack>
              <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={3}>
                {paymentMethods.virtual_account.map((bank) => (
                  <GridItem key={bank.id}>
                    <Box
                      border="2px solid"
                      borderColor={isMethodSelected(`${bank}|virtual_account`) ? 'blue.500' : 'gray.200'}
                      borderRadius="lg"
                      p={3}
                      bg={isMethodSelected(`${bank.id}|virtual_account`) ? 'blue.50' : 'white'}
                      cursor="pointer"
                      transition="all 0.2s"
                      _hover={{ borderColor: 'blue.300' }}
                      onClick={() => handleMethodToggle(`${bank.id}|virtual_account`, !isMethodSelected(`${bank.id}|virtual_account`))}
                    >
                      <VStack gap={2}>
                        <Checkbox.Root
                          checked={isMethodSelected(`${bank.id}|virtual_account`)}
                          onCheckedChange={({ checked }) => handleMethodToggle(`${bank.id}|virtual_account`, !!checked)}
                          colorPalette="blue"
                        >
                          <Checkbox.HiddenInput />
                          <Checkbox.Control />
                        </Checkbox.Root>
                        <Box boxSize="40px">
                          <Image
                            src={getMethodIcon(bank.name)}
                            alt={bank.name}
                            boxSize="40px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.setAttribute('style', 'display: block');
                            }}
                          />
                          <FaUniversity size={40} style={{ display: 'none' }} />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" textAlign="center">{bank.name}</Text>
                      </VStack>
                    </Box>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

          {/* Retail Outlets */}
          {paymentMethods.retail_outlet?.length > 0 && (
            <Box>
              <HStack mb={3}>
                <FaStore />
                <Heading size="sm">{t('Payment.retailOutlets')}</Heading>
              </HStack>
              <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={3}>
                {paymentMethods.retail_outlet.map((outlet) => (
                  <GridItem key={outlet.id}>
                    <Box
                      border="2px solid"
                      borderColor={isMethodSelected(`${outlet.id}|retail_outlet`) ? 'orange.500' : 'gray.200'}
                      borderRadius="lg"
                      p={3}
                      bg={isMethodSelected(`${outlet.id}|retail_outlet`) ? 'orange.50' : 'white'}
                      cursor="pointer"
                      transition="all 0.2s"
                      _hover={{ borderColor: 'orange.300' }}
                      onClick={() => handleMethodToggle(`${outlet.id}|retail_outlet`, !isMethodSelected(`${outlet.id}|retail_outlet`))}
                    >
                      <VStack gap={2}>
                        <Checkbox.Root
                          checked={isMethodSelected(`${outlet.id}|retail_outlet`)}
                          onCheckedChange={({ checked }) => handleMethodToggle(`${outlet.id}|retail_outlet`, !!checked)}
                          colorPalette="orange"
                        >
                          <Checkbox.HiddenInput />
                          <Checkbox.Control />
                        </Checkbox.Root>
                        <Box boxSize="40px">
                          <Image
                            src={getMethodIcon(outlet.name)}
                            alt={outlet.name}
                            boxSize="40px"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.nextElementSibling?.setAttribute('style', 'display: block');
                            }}
                          />
                          <FaStore size={40} style={{ display: 'none' }} />
                        </Box>
                        <Text fontSize="sm" fontWeight="medium" textAlign="center">{outlet.name}</Text>
                      </VStack>
                    </Box>
                  </GridItem>
                ))}
              </Grid>
            </Box>
          )}

        </VStack>

        {/* Payment Security Info */}
        <Box bg="green.50" p={4} borderRadius="md" mt={4} border="1px solid" borderColor="green.200">
          <HStack justify="center" gap={2}>
            <FaLock color="green" />
            <Text fontSize="sm" color="green.700" textAlign="center" fontWeight="medium">
              {t('Payment.securityInfo')}
            </Text>
          </HStack>
        </Box>
      </Card.Body>
    </Card.Root>
  )
}

export default PaymentMethodSelector
