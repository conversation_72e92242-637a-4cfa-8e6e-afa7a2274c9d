'use client'
import React, { useState, useEffect } from 'react'
import {
  Box,
  Card,
  Heading,
  Text,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  <PERSON><PERSON>,
  RadioGroup,
} from '@chakra-ui/react'
import { LuInfo, LuTriangleAlert } from 'react-icons/lu'
import { FaTruck, FaShippingFast, FaClock } from 'react-icons/fa'
import { useShippingOptionsQuery } from '@/services/useShippingQuery'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'

interface ShippingAddress {
  country: string
  city: string
  zipCode: string
}

interface CartItem {
  id: string
  quantity: number
  product: {
    id: string
    itemName: string
    weight?: number
  }
}

interface ShippingOption {
  carrier: string
  service: string
  cost: number
  currency: string
  estimatedDays: number
  trackingAvailable: boolean
}

interface ShippingOptionsSelectorProps {
  shippingAddress: ShippingAddress
  cartItems: CartItem[]
  onShippingSelect: (option: ShippingOption) => void
  selectedOption?: ShippingOption
}

const ShippingOptionsSelector: React.FC<ShippingOptionsSelectorProps> = ({
  shippingAddress,
  cartItems,
  onShippingSelect,
  selectedOption
}) => {
  const { formatPrice } = useCurrencyLanguage()
  const [selectedShippingId, setSelectedShippingId] = useState<string>('')

  // Only fetch shipping options if we have complete address
  const shouldFetch = Boolean(shippingAddress.country && shippingAddress.city && shippingAddress.zipCode && cartItems.length > 0)

  const { data: shippingOptions, isLoading, error } = useShippingOptionsQuery(
    'temp-address-id', // We'll need to create a temp address or use the address data directly
    cartItems,
    shouldFetch
  )

  useEffect(() => {
    if (selectedOption) {
      setSelectedShippingId(`${selectedOption.carrier}-${selectedOption.service}`)
    }
  }, [selectedOption])

  const handleShippingSelect = (details: { value: string | null }) => {
    const optionId = details.value
    if (!optionId) return

    setSelectedShippingId(optionId)

    const option = shippingOptions?.rates?.find(
      (rate: ShippingOption) => `${rate.carrier}-${rate.service}` === optionId
    )

    if (option) {
      onShippingSelect(option)
    }
  }

  const handleOptionClick = (optionId: string) => {
    handleShippingSelect({ value: optionId })
  }

  const getShippingIcon = (carrier: string) => {
    switch (carrier.toLowerCase()) {
      case 'dhl':
      case 'fedex':
      case 'ups':
        return <FaShippingFast />
      default:
        return <FaTruck />
    }
  }

  const getEstimatedDeliveryText = (days: number) => {
    if (days === 1) return '1 day'
    if (days <= 3) return `${days} days`
    if (days <= 7) return `${days} days`
    return `${days} days`
  }

  if (!shouldFetch) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Shipping Options</Heading>
        </Card.Header>
        <Card.Body>
          <Alert.Root status="info">
            <LuInfo />
            <Alert.Title>Complete shipping address to see shipping options</Alert.Title>
            <Alert.Description>
              Please fill in your country, city, and ZIP code to calculate shipping costs.
            </Alert.Description>
          </Alert.Root>
        </Card.Body>
      </Card.Root>
    )
  }

  if (isLoading) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Shipping Options</Heading>
        </Card.Header>
        <Card.Body>
          <VStack gap={4} align="center" py={8}>
            <Spinner size="lg" />
            <Text>Calculating shipping options...</Text>
          </VStack>
        </Card.Body>
      </Card.Root>
    )
  }

  if (error || !shippingOptions?.rates || shippingOptions.rates.length === 0) {
    return (
      <Card.Root>
        <Card.Header>
          <Heading size="md">Shipping Options</Heading>
        </Card.Header>
        <Card.Body>
          <Alert.Root status="warning">
            <LuTriangleAlert />
            <Alert.Title>No shipping options available</Alert.Title>
            <Alert.Description>
              We couldn't find shipping options for your location. Please contact support for assistance.
            </Alert.Description>
          </Alert.Root>
        </Card.Body>
      </Card.Root>
    )
  }

  return (
    <Card.Root>
      <Card.Header>
        <VStack align="start" gap={1}>
          <Heading size="md">Shipping Options</Heading>
          <Text fontSize="sm" color="gray.600">
            Shipping to: {shippingAddress.city}, {shippingAddress.country}
          </Text>
        </VStack>
      </Card.Header>
      <Card.Body>
        <RadioGroup.Root value={selectedShippingId} onValueChange={handleShippingSelect}>
          <VStack align="stretch" gap={3}>
            {shippingOptions.rates.map((option: ShippingOption) => {
              const optionId = `${option.carrier}-${option.service}`
              const isSelected = selectedShippingId === optionId

              return (
                <Box
                  key={optionId}
                  border="2px solid"
                  borderColor={isSelected ? 'blue.500' : 'gray.200'}
                  borderRadius="lg"
                  p={4}
                  bg={isSelected ? 'blue.50' : 'white'}
                  cursor="pointer"
                  transition="all 0.2s"
                  _hover={{ borderColor: 'blue.300' }}
                  onClick={() => handleOptionClick(optionId)}
                >
                  <HStack gap={4} align="center" w="full">
                    <RadioGroup.Item value={optionId} colorPalette="blue" />

                    <Box color="blue.600">
                      {getShippingIcon(option.carrier)}
                    </Box>

                    <VStack align="start" flex="1" gap={1}>
                      <HStack gap={2}>
                        <Text fontWeight="bold">{option.carrier}</Text>
                        <Badge colorPalette="blue" variant="subtle">
                          {option.service}
                        </Badge>
                        {option.trackingAvailable && (
                          <Badge colorPalette="green" variant="subtle" fontSize="xs">
                            Tracking Available
                          </Badge>
                        )}
                      </HStack>

                      <HStack gap={4} fontSize="sm" color="gray.600">
                        <HStack gap={1}>
                          <FaClock />
                          <Text>{getEstimatedDeliveryText(option.estimatedDays)}</Text>
                        </HStack>
                      </HStack>
                    </VStack>

                    <VStack align="end" gap={1}>
                      <Text fontWeight="bold" fontSize="lg" color="blue.600">
                        {formatPrice(option.cost, option.currency as any)}
                      </Text>
                      <Text fontSize="xs" color="gray.500">
                        {option.currency}
                      </Text>
                    </VStack>
                  </HStack>
                </Box>
              )
            })}
          </VStack>
        </RadioGroup.Root>
        
        {shippingOptions.rates.length > 0 && (
          <Box mt={4} p={3} bg="gray.50" borderRadius="md">
            <Text fontSize="xs" color="gray.600">
              * Shipping costs are calculated based on weight and destination
              <br />
              * Delivery times are estimates and may vary
            </Text>
          </Box>
        )}
      </Card.Body>
    </Card.Root>
  )
}

export default ShippingOptionsSelector
